"""
增强的回复引擎
支持FAQ和产品关键词匹配
"""
import os
import sys
import jieba
from typing import Optional, Dict, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

from src.database.enhanced_reader import EnhancedFAQReader, EnhancedProductReader, format_product_reply
from src.utils.logger import get_logger

logger = get_logger(__name__)

class EnhancedReplyEngine:
    """增强的回复引擎"""
    
    def __init__(self):
        self.data_dir = os.path.join(project_root, 'data')
        
        # 初始化数据读取器
        faq_file = os.path.join(self.data_dir, 'faq_enhanced.xlsx')
        products_file = os.path.join(self.data_dir, 'products_enhanced.xlsx')
        
        self.faq_reader = EnhancedFAQReader(faq_file)
        self.product_reader = EnhancedProductReader(products_file)
        
        # 产品相关关键词
        self.product_keywords = [
            '产品', '商品', '价格', '多少钱', '购买', '买', 
            '手机', '耳机', '电脑', '充电器', '鼠标', '手表',
            '推荐', '介绍', '详情', '参数', '配置'
        ]
        
        logger.info("增强回复引擎初始化完成")
    
    def generate_reply(self, message: str, sender: str = "", context: Dict = None) -> str:
        """生成回复"""
        try:
            message = message.strip()
            if not message:
                return "您好！有什么可以帮助您的吗？"
            
            logger.info(f"处理消息: {message}")
            
            # 1. 首先尝试FAQ匹配
            faq_result = self.faq_reader.search_faq(message)
            if faq_result and faq_result['score'] > 0.7:
                logger.info(f"FAQ匹配成功: {faq_result['question']} (分数: {faq_result['score']:.2f})")
                return faq_result['answer']
            
            # 2. 检查是否是产品相关查询
            if self._is_product_query(message):
                products = self.product_reader.search_products(message)
                if products:
                    logger.info(f"产品匹配成功: 找到 {len(products)} 个产品")
                    return format_product_reply(products)
            
            # 3. 如果FAQ有低分匹配，使用它
            if faq_result and faq_result['score'] > 0.5:
                logger.info(f"FAQ低分匹配: {faq_result['question']} (分数: {faq_result['score']:.2f})")
                return faq_result['answer']
            
            # 4. 尝试AI回复
            if hasattr(self, 'llm_service') and self.llm_service:
                try:
                    from src.ai.llm_service import LLMService
                    from config import config

                    llm_service = LLMService(
                        api_key=config.ai.api_key,
                        base_url=config.ai.base_url,
                        model=config.ai.model,
                        max_tokens=config.ai.max_tokens,
                        temperature=config.ai.temperature,
                        system_prompt=config.ai.system_prompt
                    )

                    ai_reply = llm_service.generate_reply(message)
                    if ai_reply:
                        logger.info("使用AI回复")
                        return ai_reply
                except Exception as e:
                    logger.debug(f"AI回复失败: {e}")

            # 5. 无法回复时保持沉默，让人工处理
            logger.info("无法匹配，保持沉默")
            return ""  # 返回空字符串，不回复
            
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return ""  # 异常时也保持沉默，让人工处理
    
    def _is_product_query(self, message: str) -> bool:
        """判断是否是产品查询"""
        message_lower = message.lower()
        
        # 检查产品关键词
        for keyword in self.product_keywords:
            if keyword in message_lower:
                return True
        
        # 检查是否包含具体产品名称
        if self.product_reader.data is not None and not self.product_reader.data.empty:
            for _, row in self.product_reader.data.iterrows():
                product_name = str(row['产品名称']).lower()
                keywords = str(row.get('产品关键词', '')).lower().split(',')
                
                if product_name in message_lower:
                    return True
                
                for keyword in keywords:
                    keyword = keyword.strip()
                    if keyword and keyword in message_lower:
                        return True
        
        return False
    

    
    def get_product_categories(self) -> List[str]:
        """获取产品分类"""
        return self.product_reader.get_categories()
    
    def get_products_by_category(self, category: str) -> List[Dict]:
        """根据分类获取产品"""
        return self.product_reader.get_products_by_category(category)
    
    def search_products(self, query: str, max_results: int = 3) -> List[Dict]:
        """搜索产品"""
        return self.product_reader.search_products(query, max_results=max_results)
    
    def reload_data(self):
        """重新加载数据"""
        try:
            self.faq_reader.load_data()
            self.product_reader.load_data()
            logger.info("数据重新加载完成")
        except Exception as e:
            logger.error(f"重新加载数据失败: {e}")
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        try:
            # 检查AI服务可用性
            ai_available = False
            try:
                from src.ai.llm_service import LLMService
                from config import config

                llm_service = LLMService(
                    api_key=config.ai.api_key,
                    base_url=config.ai.base_url,
                    model=config.ai.model,
                    max_tokens=config.ai.max_tokens,
                    temperature=config.ai.temperature,
                    system_prompt=config.ai.system_prompt
                )
                ai_available = llm_service.is_available()
            except Exception as e:
                logger.debug(f"检查AI服务失败: {e}")
                ai_available = False

            stats = {
                'faq_count': len(self.faq_reader.data) if self.faq_reader.data is not None else 0,
                'product_count': len(self.product_reader.data) if self.product_reader.data is not None else 0,
                'product_categories': len(self.get_product_categories()),
                'faq_categories': self.faq_reader.get_categories(),
                'ai_available': ai_available,
                'status': 'normal'
            }

            # 检查数据状态
            if stats['faq_count'] == 0:
                stats['status'] = 'no_faq_data'
            elif stats['product_count'] == 0:
                stats['status'] = 'no_product_data'

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                'faq_count': 0,
                'product_count': 0,
                'product_categories': 0,
                'faq_categories': [],
                'ai_available': False,
                'status': 'error',
                'error': str(e)
            }


def test_enhanced_reply_engine():
    """测试增强回复引擎"""
    print("测试增强回复引擎")
    print("=" * 50)
    
    engine = EnhancedReplyEngine()
    
    # 测试FAQ
    faq_questions = [
        "如何退货",
        "什么时候发货", 
        "有什么优惠",
        "怎么联系客服"
    ]
    
    print("FAQ测试:")
    for question in faq_questions:
        reply = engine.generate_reply(question)
        print(f"\n问题: {question}")
        print(f"回复: {reply[:100]}...")
    
    # 测试产品查询
    product_queries = [
        "推荐一款手机",
        "有什么耳机",
        "充电器多少钱",
        "笔记本电脑参数"
    ]
    
    print("\n\n产品查询测试:")
    for query in product_queries:
        reply = engine.generate_reply(query)
        print(f"\n查询: {query}")
        print(f"回复: {reply[:150]}...")
    
    # 统计信息
    stats = engine.get_statistics()
    print(f"\n\n统计信息:")
    print(f"FAQ数量: {stats['faq_count']}")
    print(f"产品数量: {stats['product_count']}")
    print(f"产品分类: {stats['product_categories']}")
    print(f"状态: {stats['status']}")


if __name__ == "__main__":
    test_enhanced_reply_engine()
