"""
微信消息处理模块
基于原项目简化，专注于客服场景的消息处理
"""
import os
import sys
import time
import threading
from typing import Optional, Dict, Any, List, Tuple

try:
    from wxauto import WeChat
    WXAUTO_AVAILABLE = True
except ImportError:
    WXAUTO_AVAILABLE = False
    WeChat = None

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

# 尝试导入支持图片的增强回复引擎，如果失败则使用原版
try:
    from src.bot.enhanced_reply_engine_with_images import get_enhanced_reply_engine_with_images
    ENHANCED_MODE = True
    USE_IMAGE_ENGINE = True
except ImportError:
    try:
        from src.bot.enhanced_reply_engine import EnhancedReplyEngine as ReplyEngine
        ENHANCED_MODE = True
        USE_IMAGE_ENGINE = False
    except ImportError:
        from src.bot.reply_engine import ReplyEngine
        ENHANCED_MODE = False
        USE_IMAGE_ENGINE = False
from src.utils.logger import get_logger
from src.utils.safe_message_getter import safe_get_new_message, check_wx_instance_health
from config import config

logger = get_logger("wechat_handler")


class WeChatHandler:
    """微信消息处理器"""
    
    def __init__(self):
        self.wx = None
        # 初始化回复引擎
        if USE_IMAGE_ENGINE:
            self.reply_engine = get_enhanced_reply_engine_with_images()
        else:
            self.reply_engine = ReplyEngine()
        self.running = False
        self.listen_thread = None
        self.stop_event = threading.Event()

        # 消息去重机制
        self.processed_messages = set()  # 存储已处理的消息ID
        self.message_cache_size = 1000   # 缓存大小限制

        logger.info("微信处理器初始化完成")

    def _generate_message_id(self, msg) -> str:
        """生成消息的唯一ID"""
        try:
            import hashlib

            # 获取消息属性
            sender = getattr(msg, 'sender', '') or getattr(msg, 'from', '') or ''
            content = getattr(msg, 'content', '') or getattr(msg, 'text', '') or ''
            msg_time = getattr(msg, 'time', None) or getattr(msg, 'timestamp', None)

            # 如果有消息时间，使用时间戳
            if msg_time:
                time_str = str(msg_time)
            else:
                # 如果没有时间戳，使用当前时间（精确到秒）
                import time
                time_str = str(int(time.time()))

            # 生成唯一ID：发送者 + 内容 + 时间戳
            unique_string = f"{sender}|{content}|{time_str}"

            # 使用MD5生成短的唯一ID
            message_id = hashlib.md5(unique_string.encode('utf-8')).hexdigest()

            return message_id

        except Exception as e:
            logger.debug(f"生成消息ID失败: {e}")
            # 降级方案：使用简单的字符串组合
            sender = getattr(msg, 'sender', 'unknown')
            content = getattr(msg, 'content', '')[:50]  # 取前50个字符
            return f"{sender}_{hash(content)}"

    def _is_message_processed(self, message_id: str) -> bool:
        """检查消息是否已经处理过"""
        if message_id in self.processed_messages:
            return True

        # 添加到已处理集合
        self.processed_messages.add(message_id)

        # 限制缓存大小，防止内存泄漏
        if len(self.processed_messages) > self.message_cache_size:
            # 移除最旧的一半消息ID
            old_messages = list(self.processed_messages)[:self.message_cache_size // 2]
            for old_id in old_messages:
                self.processed_messages.discard(old_id)
            logger.debug(f"清理消息缓存，保留 {len(self.processed_messages)} 条记录")

        return False

    def initialize_wechat(self, max_retries: int = 5) -> bool:
        """初始化微信连接，支持重试"""
        if not WXAUTO_AVAILABLE:
            logger.error("wxauto库未安装，请运行: pip install wxauto")
            return False

        # 检查微信进程是否运行
        import psutil
        wechat_running = False
        for proc in psutil.process_iter(['name']):
            try:
                if 'wechat' in proc.info['name'].lower():
                    wechat_running = True
                    break
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if not wechat_running:
            logger.error("微信PC版未运行，请先启动微信并登录")
            return False

        for attempt in range(max_retries):
            try:
                logger.info(f"正在初始化微信连接... (尝试 {attempt + 1}/{max_retries})")

                # 创建微信对象
                self.wx = WeChat()

                # 等待微信对象完全初始化 - 增加等待时间
                logger.debug("等待微信对象初始化...")
                time.sleep(5.0)  # 增加到5秒

                # 基本验证微信对象（跳过深度验证）
                if not self._basic_validate_wechat_object():
                    if attempt < max_retries - 1:
                        logger.warning(f"微信对象基本验证失败，{5}秒后重试...")
                        time.sleep(5)
                        continue
                    else:
                        logger.error("微信对象基本验证失败，已达到最大重试次数")
                        return False

                logger.info(f"微信连接成功，当前用户: {self.wx.nickname}")
                logger.debug("微信对象验证通过")
                return True

            except Exception as e:
                logger.error(f"微信初始化异常 (尝试 {attempt + 1}): {e}")

                # 特殊处理窗口句柄错误
                if "SetWindowPos" in str(e) or "无效的窗口句柄" in str(e):
                    logger.error("检测到窗口句柄错误，可能的原因:")
                    logger.error("  1. 微信PC版未完全启动")
                    logger.error("  2. 微信版本与wxauto不兼容")
                    logger.error("  3. 微信窗口被其他程序占用")

                if attempt < max_retries - 1:
                    logger.info(f"{5}秒后重试...")
                    time.sleep(5)
                else:
                    logger.error("微信初始化失败，已达到最大重试次数")
                    return False

        return False

    def _basic_validate_wechat_object(self) -> bool:
        """基本验证微信对象的有效性（不进行深度测试）"""
        try:
            # 基本对象检查
            if not self.wx:
                logger.debug("微信对象为None")
                return False

            # 检查nickname属性
            if not hasattr(self.wx, 'nickname'):
                logger.debug("微信对象缺少nickname属性")
                return False

            # 尝试获取nickname
            nickname = self.wx.nickname
            if not nickname:
                logger.debug("无法获取微信昵称")
                return False

            # 检查关键方法存在性（不调用）
            if not hasattr(self.wx, 'GetNewMessage'):
                logger.debug("微信对象缺少GetNewMessage方法")
                return False

            logger.debug("微信对象基本验证通过")
            return True

        except Exception as e:
            logger.debug(f"微信对象基本验证异常: {e}")
            return False

    def _validate_wechat_object(self) -> bool:
        """验证微信对象的有效性"""
        try:
            # 基本对象检查
            if not self.wx:
                logger.debug("微信对象为None")
                return False

            # 检查nickname属性
            if not hasattr(self.wx, 'nickname'):
                logger.debug("微信对象缺少nickname属性")
                return False

            # 尝试获取nickname
            nickname = self.wx.nickname
            if not nickname:
                logger.debug("无法获取微信昵称")
                return False

            # 检查关键方法
            if not hasattr(self.wx, 'GetNewMessage'):
                logger.debug("微信对象缺少GetNewMessage方法")
                return False

            # 跳过深度验证，因为GetNewMessage可能在初始化时不稳定
            # 我们将在运行时处理这个问题
            logger.debug("跳过GetNewMessage深度验证，将在运行时处理")

            return True

        except Exception as e:
            logger.debug(f"微信对象验证异常: {e}")
            return False
            
            # 检查监听配置
            if getattr(config.wechat, 'listen_all', False):
                logger.info("已启用全局监听模式，将监听所有消息")
            elif not config.wechat.listen_list:
                logger.info("监听列表为空，将启用全局监听模式（除自己的消息）")
            else:
                # 验证监听列表中的聊天对象
                valid_chats = []
                for chat_name in config.wechat.listen_list:
                    try:
                        # 尝试切换到聊天窗口来验证聊天对象是否存在
                        if self.wx.ChatWith(chat_name):
                            valid_chats.append(chat_name)
                            logger.info(f"验证聊天对象成功: {chat_name}")
                        else:
                            logger.warning(f"找不到聊天对象: {chat_name}")
                    except Exception as e:
                        logger.error(f"验证聊天对象失败 {chat_name}: {e}")

                if valid_chats:
                    logger.info(f"将监听以下聊天对象: {valid_chats}")
                else:
                    logger.warning("监听列表中没有有效的聊天对象，将启用全局监听模式")
            
            logger.info("微信初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"微信初始化失败: {e}")
            return False
    
    def start_listening(self):
        """开始监听消息"""
        if self.running:
            logger.warning("消息监听已在运行中")
            return
        
        if not self.initialize_wechat():
            logger.error("微信初始化失败，无法开始监听")
            return
        
        self.running = True
        self.stop_event.clear()
        self.listen_thread = threading.Thread(target=self._message_listener, daemon=True)
        self.listen_thread.start()
        
        logger.info("开始监听微信消息")
    
    def stop_listening(self):
        """停止监听消息"""
        if not self.running:
            return
        
        self.running = False
        self.stop_event.set()
        
        if self.listen_thread and self.listen_thread.is_alive():
            self.listen_thread.join(timeout=5)
        
        logger.info("停止监听微信消息")
    
    def _message_listener(self):
        """消息监听循环"""
        last_window_check = 0
        check_interval = 600  # 10分钟检查一次窗口

        # 显示监听状态
        if getattr(config.wechat, 'listen_all', False):
            logger.info("开始消息监听循环... [全局监听模式]")
        elif not config.wechat.listen_list:
            logger.info("开始消息监听循环... [自动全局监听模式 - 监听列表为空]")
        else:
            logger.info(f"开始消息监听循环... [指定监听模式 - {len(config.wechat.listen_list)}个对象]")

        while not self.stop_event.is_set():
            try:
                current_time = time.time()

                # 定期检查微信窗口状态
                if current_time - last_window_check > check_interval:
                    try:
                        # 检查微信是否还在运行
                        if not hasattr(self.wx, 'nickname') or not self.wx.nickname:
                            logger.warning("微信连接丢失，尝试重新初始化")
                            if not self.initialize_wechat():
                                time.sleep(30)
                                continue
                    except Exception as e:
                        logger.warning(f"微信状态检查失败: {e}")
                    last_window_check = current_time

                # 使用替代方法获取消息
                try:
                    msgs = None

                    # 检查微信连接状态
                    if self.wx is None:
                        logger.warning("微信连接丢失，尝试重新连接...")
                        if not self.initialize_wechat():
                            logger.error("微信重连失败，等待5秒后重试")
                            time.sleep(5)
                            continue

                    # 尝试获取消息，使用宽容的错误处理
                    msgs = None

                    # 基本检查
                    if not self.wx:
                        logger.warning("微信对象为None")
                        continue

                    # 使用安全的消息获取方法，避免wxauto内部错误
                    msgs = safe_get_new_message(self.wx)

                    # 如果安全方法也没有获取到消息，检查微信实例健康状态
                    if not msgs and not check_wx_instance_health(self.wx):
                        logger.warning("微信实例健康检查失败，尝试重新初始化")
                        self.wx = None
                        continue

                    if not msgs:
                        continue

                    # 处理每条消息
                    for msg in msgs:
                        try:
                            # 生成消息唯一ID
                            message_id = self._generate_message_id(msg)

                            # 检查是否已经处理过这条消息
                            if self._is_message_processed(message_id):
                                logger.debug(f"跳过重复消息: {message_id}")
                                continue

                            # 检查消息是否来自监听列表中的聊天对象
                            if self._should_process_message(msg):
                                sender = getattr(msg, 'sender', 'Unknown')
                                content = getattr(msg, 'content', '')
                                logger.info(f"处理消息: {sender} - {content[:30]}... (ID: {message_id[:8]})")
                                self._process_message(msg)
                            else:
                                sender = getattr(msg, 'sender', 'Unknown')
                                content = getattr(msg, 'content', '')
                                logger.debug(f"跳过消息: {sender} - {content[:30]}... (ID: {message_id[:8]})")

                        except Exception as e:
                            logger.error(f"处理单条消息失败: {e}")

                except Exception as e:
                    logger.debug(f"获取消息失败: {e}")

                # 短暂休眠，避免过度占用CPU
                time.sleep(1)

            except Exception as e:
                logger.error(f"消息监听循环异常: {e}")
                time.sleep(5)

    def _should_process_message(self, msg) -> bool:
        """判断是否应该处理这条消息"""
        try:
            # 获取消息发送者
            sender = getattr(msg, 'sender', '') or getattr(msg, 'from', '')
            content = getattr(msg, 'content', '') or getattr(msg, 'text', '')

            # 跳过空消息
            if not content or not content.strip():
                return False

            # 跳过自己发送的消息
            if sender == "self" or sender == getattr(self.wx, 'nickname', ''):
                return False

            # 跳过系统消息
            if sender in ['system', 'System', ''] or not sender:
                return False

            # 如果开启了全部监听模式
            if getattr(config.wechat, 'listen_all', False):
                logger.debug(f"全局监听模式，处理消息: {sender}")
                return True

            # 如果监听列表为空，启用全局监听（除了自己的消息）
            if not config.wechat.listen_list:
                logger.debug(f"监听列表为空，启用全局监听: {sender}")
                return True

            # 检查发送者是否在监听列表中
            for chat_name in config.wechat.listen_list:
                if chat_name in sender or sender in chat_name:
                    logger.debug(f"发送者在监听列表中: {sender} -> {chat_name}")
                    return True

            # 如果没有匹配，不处理
            logger.debug(f"发送者不在监听列表中: {sender}")
            return False

        except Exception as e:
            logger.debug(f"判断消息处理失败: {e}")
            return False
    
    def _process_message(self, msg):
        """处理单条消息"""
        try:
            # 获取消息类型信息
            msg_class_name = type(msg).__name__
            msg_module = type(msg).__module__

            logger.debug(f"收到消息对象 - 类型: {msg_class_name}, 模块: {msg_module}")

            # 忽略自己发送的消息
            if 'self.' in msg_module or 'Self' in msg_class_name:
                logger.debug(f"忽略自己发送的消息: {msg_class_name}")
                return

            # 获取消息属性
            sender = ""
            content = ""
            msg_type = "unknown"

            # 获取发送者和内容（wxauto消息对象都有这些属性）
            if hasattr(msg, 'sender'):
                sender = str(msg.sender) if msg.sender else ""

            if hasattr(msg, 'content'):
                content = str(msg.content) if msg.content else ""

            # 过滤自己发送的消息
            if sender == "self":
                logger.debug("忽略自己发送的消息 (sender=self)")
                return

            # 判断消息类型
            if 'Text' in msg_class_name:
                msg_type = "text"
            elif 'Image' in msg_class_name:
                msg_type = "image"
            elif 'Voice' in msg_class_name:
                msg_type = "voice"
                # 尝试获取语音文件路径
                if hasattr(msg, 'path'):
                    voice_path = getattr(msg, 'path', None)
                    if voice_path and voice_path != content:
                        logger.info(f"检测到语音文件路径: {voice_path}")
                        content = voice_path
                elif hasattr(msg, 'file_path'):
                    voice_path = getattr(msg, 'file_path', None)
                    if voice_path and voice_path != content:
                        logger.info(f"检测到语音文件路径: {voice_path}")
                        content = voice_path
                elif hasattr(msg, 'voice_path'):
                    voice_path = getattr(msg, 'voice_path', None)
                    if voice_path and voice_path != content:
                        logger.info(f"检测到语音文件路径: {voice_path}")
                        content = voice_path
            elif 'File' in msg_class_name:
                msg_type = "file"
            elif 'Other' in msg_class_name:
                # OtherMessage通常是系统消息或时间戳，忽略
                logger.debug(f"忽略其他类型消息: {content}")
                return
            else:
                msg_type = "unknown"

            logger.debug(f"消息解析结果 - 发送者: {sender}, 内容: {content[:50]}..., 类型: {msg_type}")

            # 兼容性处理：如果是列表或元组格式
            if isinstance(msg, (list, tuple)) and len(msg) >= 2:
                sender = str(msg[0]) if msg[0] else sender
                content = str(msg[1]) if msg[1] else content
                msg_type = str(msg[2]) if len(msg) > 2 else "text"
            elif isinstance(msg, dict):
                # 如果是字典格式
                sender = str(msg.get('sender', sender))
                content = str(msg.get('content', content))
                msg_type = str(msg.get('type', msg_type))

            # 过滤空消息
            if not content:
                logger.debug("忽略空消息")
                return

            # 过滤自己发送的消息
            if not sender:
                logger.debug("忽略无发送者的消息")
                return

            # 过滤自己发送的消息（如果有昵称信息）
            try:
                if hasattr(self.wx, 'nickname') and sender == self.wx.nickname:
                    logger.debug("忽略自己发送的消息")
                    return
            except:
                pass

            # 添加详细的消息调试信息
            logger.info(f"消息详细信息 - 类型: {msg_type}, 内容: {content[:100]}, 发送者: {sender}")
            logger.info(f"消息对象类名: {msg_class_name}, 模块: {msg_module}")

            # 处理文本和语音消息
            if msg_type not in ['text', 'voice', 'audio']:
                logger.debug(f"忽略非文本/语音消息: {msg_type}")
                return

            # 如果是语音消息，尝试转换为文字
            if msg_type in ['voice', 'audio']:
                logger.info(f"检测到语音消息 - 类型: {msg_type}, 发送者: {sender}")
                logger.info(f"语音配置状态: voice_to_text={config.wechat.voice_to_text}")
                logger.info(f"语音内容: {content}")

                # 检查是否启用语音转文字
                if not config.wechat.voice_to_text:
                    logger.warning("语音转文字功能已禁用，忽略语音消息")
                    return

                # 🎯 首先尝试 to_text 方法！
                logger.info(f"🎯 检查关键的 to_text 方法:")
                if hasattr(msg, 'to_text'):
                    logger.info(f"  ✅ 找到 to_text 方法!")
                    try:
                        # 尝试调用 to_text 方法
                        logger.info(f"  🔧 尝试调用 to_text() 方法...")
                        text_result = msg.to_text()
                        logger.info(f"  🎉 to_text() 调用成功: {text_result}")

                        # 如果成功获取到文字，直接使用
                        if text_result and text_result != content and not self._is_voice_placeholder(text_result):
                            logger.info(f"  ✅ 获取到有效的语音文字: {text_result}")
                            content = text_result  # 直接替换内容
                            msg_type = "text"  # 改为文本类型处理
                            logger.info(f"  🎯 消息类型已改为文本，跳出语音处理流程")
                            # 直接跳转到文本处理，不再执行后续语音处理逻辑
                        else:
                            logger.warning(f"  ⚠️ to_text() 返回无效结果: {text_result}")
                    except Exception as e:
                        logger.warning(f"  ❌ to_text() 调用失败: {e}")
                else:
                    logger.warning(f"  ❌ 未找到 to_text 方法")

                # 如果 to_text 成功，msg_type 已经改为 text，不会进入下面的语音处理逻辑
                if msg_type in ['voice', 'audio']:
                    # 详细调试语音消息对象
                    logger.info(f"🎤 语音消息对象详细调试开始")
                    logger.info(f"语音消息对象类型: {type(msg)}")
                    logger.info(f"语音消息对象ID: {id(msg)}")

                    # 获取所有属性
                    all_attrs = [attr for attr in dir(msg) if not attr.startswith('_')]
                    logger.info(f"所有属性 ({len(all_attrs)}个): {all_attrs}")

                    # 详细检查每个属性的值
                    for attr in all_attrs:
                        try:
                            value = getattr(msg, attr, None)
                            value_type = type(value).__name__

                            if isinstance(value, str):
                                display_value = value[:100] + "..." if len(value) > 100 else value
                                logger.info(f"  属性 {attr} ({value_type}): {display_value}")
                            elif callable(value):
                                logger.info(f"  方法 {attr}: <可调用方法>")
                            else:
                                logger.info(f"  属性 {attr} ({value_type}): {str(value)[:100]}")
                        except Exception as e:
                            logger.info(f"  属性 {attr}: <获取失败: {e}>")

                    logger.info(f"🎤 语音消息对象详细调试结束")

                    # 检查微信对象的语音API
                    has_get_voice_text = hasattr(self.wx, 'GetVoiceText') if self.wx else False
                    has_voice_to_text = hasattr(self.wx, 'VoiceToText') if self.wx else False
                    logger.info(f"微信API可用性: GetVoiceText={has_get_voice_text}, VoiceToText={has_voice_to_text}")

                    # 如果 to_text 失败，发送用户引导
                    logger.warning("语音转文字失败，发送用户引导")
                    guidance = self._get_voice_guidance_message()
                    self._send_reply(guidance, sender)
                    return
            
            logger.info(f"收到消息 - 发送者: {sender}, 内容: {content[:50]}...")

            # 检查是否需要自动回复
            if not config.wechat.auto_reply:
                logger.debug("自动回复已禁用")
                return

            # 生成回复
            reply, image_paths = self._generate_reply(content, sender, sender)  # 使用sender作为chat_id
            if reply:
                # 添加回复延迟
                if config.wechat.reply_delay > 0:
                    time.sleep(config.wechat.reply_delay)

                # 发送文本回复
                self._send_reply(reply, sender)

                # 发送产品图片
                if image_paths:
                    self._send_product_images(image_paths, sender)

                logger.info(f"已回复: {reply[:50]}... (图片: {len(image_paths)}张)")
            
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
    
    def _generate_reply(self, content: str, sender: str, chat_id: str) -> Tuple[Optional[str], List[str]]:
        """生成回复内容（返回文本和图片路径）"""
        try:
            # 判断是否为群聊
            is_group = self._is_group_chat(chat_id)
            sender_name = sender if is_group else ""

            # 使用统一的回复引擎
            if USE_IMAGE_ENGINE:
                # 使用支持图片和AI的回复引擎
                reply, image_paths = self.reply_engine.generate_reply_with_images(content, sender_name)
                return reply, image_paths
            else:
                # 使用普通回复引擎
                reply = self.reply_engine.generate_reply(content, sender_name)
                return reply, []

        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return None, []

    def _is_product_query(self, content: str) -> bool:
        """判断是否是产品查询"""
        product_keywords = [
            '手机', '耳机', '电脑', '笔记本', '鼠标', '键盘', '充电器', '手表',
            '推荐', '价格', '多少钱', '怎么样', '好用吗', '性能', '配置',
            '买', '购买', '选择', '对比', '哪个好', '哪款'
        ]
        content_lower = content.lower()
        return any(keyword in content_lower for keyword in product_keywords)

    def _send_product_images(self, image_paths: List[str], chat_name: str):
        """发送产品图片"""
        try:
            for img_path in image_paths:
                if os.path.exists(img_path):
                    # 切换到对应聊天窗口并发送图片
                    if self.wx.ChatWith(chat_name):
                        self.wx.SendFiles(filepath=img_path)
                        logger.info(f"已发送产品图片: {os.path.basename(img_path)}")
                        time.sleep(1)  # 添加发送间隔，避免刷屏
                    else:
                        logger.error(f"无法切换到聊天窗口: {chat_name}")
                else:
                    logger.warning(f"产品图片不存在: {img_path}")
        except Exception as e:
            logger.error(f"发送产品图片失败: {e}")

    def _convert_voice_to_text(self, voice_content: str, sender: str) -> Optional[str]:
        """
        将语音消息转换为文字

        Args:
            voice_content: 语音消息内容（可能是文件路径或其他标识）
            sender: 发送者

        Returns:
            Optional[str]: 转换后的文字内容，失败时返回None
        """
        logger.info(f"开始语音转文字 - 内容: {voice_content}, 发送者: {sender}")

        try:
            # 方法1: 尝试使用微信自带的语音转文字功能
            logger.debug(f"尝试方法1: GetVoiceText")
            if hasattr(self.wx, 'GetVoiceText'):
                try:
                    logger.debug(f"调用 GetVoiceText({voice_content})")
                    text = self.wx.GetVoiceText(voice_content)
                    logger.debug(f"GetVoiceText 返回: {text}")
                    if text and text.strip():
                        logger.info(f"微信语音转文字成功: {text}")
                        return text.strip()
                    else:
                        logger.debug(f"GetVoiceText 返回空结果")
                except Exception as e:
                    logger.debug(f"微信语音转文字失败: {e}")
            else:
                logger.debug(f"微信对象不支持 GetVoiceText 方法")

            # 方法2: 尝试使用微信的语音识别API
            logger.debug(f"尝试方法2: VoiceToText")
            if hasattr(self.wx, 'VoiceToText'):
                try:
                    logger.debug(f"调用 VoiceToText({voice_content})")
                    text = self.wx.VoiceToText(voice_content)
                    logger.debug(f"VoiceToText 返回: {text}")
                    if text and text.strip():
                        logger.info(f"微信VoiceToText成功: {text}")
                        return text.strip()
                    else:
                        logger.debug(f"VoiceToText 返回空结果")
                except Exception as e:
                    logger.debug(f"微信VoiceToText失败: {e}")
            else:
                logger.debug(f"微信对象不支持 VoiceToText 方法")

            # 方法3: 检查是否为语音占位符（但不立即返回，继续尝试其他方法）
            if self._is_voice_placeholder(voice_content):
                logger.warning(f"检测到语音占位符: {voice_content}")
                # 不立即返回None，继续尝试百度识别等其他方法

            # 方法4: 如果语音内容本身就是文字（某些情况下）
            if isinstance(voice_content, str) and len(voice_content) > 0:
                # 检查是否看起来像文字而不是文件路径
                if (not self._looks_like_file_path(voice_content) and
                    not self._is_voice_placeholder(voice_content) and
                    len(voice_content) > 5):  # 至少5个字符
                    logger.info(f"语音内容似乎已是文字: {voice_content}")
                    return voice_content

            # 所有方法都失败了

            logger.warning(f"无法转换语音消息: {voice_content}")
            return None

        except Exception as e:
            logger.error(f"语音转文字异常: {e}")
            return None



    def _is_voice_placeholder(self, content: str) -> bool:
        """
        检查是否为语音占位符文本

        Args:
            content: 消息内容

        Returns:
            bool: 是否为语音占位符
        """
        if not isinstance(content, str):
            return False

        # 常见的语音占位符模式
        voice_patterns = [
            "[语音]",
            "语音消息",
            "未播放",
            "秒",
            "voice message",
            "audio message"
        ]

        content_lower = content.lower()

        # 检查是否包含语音相关关键词
        voice_keywords_count = 0
        for pattern in voice_patterns:
            if pattern.lower() in content_lower:
                voice_keywords_count += 1

        # 如果包含多个语音关键词，很可能是占位符
        if voice_keywords_count >= 2:
            return True

        # 检查特定模式: [语音]X秒,未播放
        if "[语音]" in content and ("秒" in content or "未播放" in content):
            return True

        return False

    def _looks_like_file_path(self, content: str) -> bool:
        """检查内容是否像文件路径"""
        if not content:
            return False

        # 检查是否包含路径分隔符
        if '/' in content or '\\' in content:
            return True

        # 检查是否有音频文件扩展名
        audio_extensions = ['.amr', '.wav', '.mp3', '.m4a', '.silk', '.pcm', '.aac', '.flac']
        if '.' in content:
            ext = '.' + content.split('.')[-1].lower()
            if ext in audio_extensions:
                return True

        # 检查是否很短且无空格（可能是文件ID）
        if len(content) < 30 and not any(c.isspace() for c in content) and not any(c.isalpha() and ord(c) > 127 for c in content):
            return True

        return False





    def _get_voice_guidance_message(self) -> str:
        """获取语音消息引导文本"""
        guidance_messages = [
            "收到您的语音消息了！😊",
            "",
            "为了更好地为您服务，建议您：",
            "📝 直接发送文字消息",
            "🎤 或在微信中点击语音旁的\"转文字\"按钮",
            "",
            "这样我就能更准确地理解并帮助您了！"
        ]

        return "\n".join(guidance_messages)

    def _send_reply(self, reply: str, chat_name: str):
        """发送回复"""
        try:
            # 切换到对应聊天窗口并发送消息
            if self.wx.ChatWith(chat_name):
                self.wx.SendMsg(msg=reply)
                logger.debug(f"消息已发送到: {chat_name}")
            else:
                logger.error(f"无法切换到聊天窗口: {chat_name}")

        except Exception as e:
            logger.error(f"发送回复失败: {e}")
    
    def _is_group_chat(self, chat_id: str) -> bool:
        """判断是否为群聊"""
        try:
            # 简单判断：群聊名称通常包含多个字符
            # 这里可以根据实际情况调整判断逻辑
            return len(chat_id) > 10 or '群' in chat_id
        except:
            return False
    
    def send_manual_message(self, message: str, chat_id: str) -> bool:
        """手动发送消息"""
        try:
            if not self.wx:
                logger.error("微信未初始化")
                return False
            
            if self.wx.ChatWith(chat_id):
                self.wx.SendMsg(msg=message, who=chat_id)
                logger.info(f"手动发送消息成功: {message[:50]}...")
                return True
            else:
                logger.error(f"找不到聊天对象: {chat_id}")
                return False
                
        except Exception as e:
            logger.error(f"手动发送消息失败: {e}")
            return False
    
    def get_chat_list(self) -> list:
        """获取聊天列表"""
        try:
            if not self.wx:
                return []

            # wxauto库没有直接获取所有会话的方法
            # 这里返回配置中的监听列表作为参考
            return config.wechat.listen_list

        except Exception as e:
            logger.error(f"获取聊天列表失败: {e}")
            return []
    
    def get_status(self) -> Dict[str, Any]:
        """获取处理器状态"""
        return {
            'running': self.running,
            'wechat_connected': self.wx is not None,
            'listen_list': config.wechat.listen_list,
            'auto_reply': config.wechat.auto_reply,
            'reply_delay': config.wechat.reply_delay
        }
