"""
配置管理模块
负责加载和管理机器人的各项配置
"""
import json
import os
from dataclasses import dataclass
from typing import List, Dict, Any


@dataclass
class WeChatSettings:
    """微信相关设置"""
    listen_list: List[str]  # 监听的聊天对象列表
    listen_all: bool = False  # 是否监听所有消息
    auto_reply: bool = True  # 是否自动回复
    reply_delay: int = 2  # 回复延迟（秒）
    voice_to_text: bool = True  # 是否启用语音转文字
    voice_reply_enabled: bool = True  # 是否对语音消息进行回复


@dataclass
class AISettings:
    """AI模型设置"""
    api_key: str
    base_url: str = "https://api.siliconflow.cn/v1"
    model: str = "Qwen/Qwen2.5-7B-Instruct"
    max_tokens: int = 1000
    temperature: float = 0.7
    enabled: bool = True
    system_prompt: str = "你是一个热情友好的销售顾问，对各类产品都很了解。说话自然随和，就像朋友聊天一样。不会说自己是AI或机器人，不会提到联系客服。"


@dataclass
class DatabaseSettings:
    """数据库设置"""
    faq_file: str = "data/faq.xlsx"
    products_file: str = "data/products.xlsx"
    similarity_threshold: float = 0.7  # 相似度阈值


@dataclass
class ReplySettings:
    """回复策略设置"""
    priority_faq: bool = True  # 优先使用FAQ
    priority_products: bool = True  # 优先使用产品库
    use_ai_fallback: bool = True  # 无匹配时使用AI
    default_reply: str = ""


@dataclass
class WebSettings:
    """Web界面设置"""
    host: str = "127.0.0.1"
    port: int = 5000
    debug: bool = False
    secret_key: str = "wchat-secret-key"
    password: str = "admin123"  # 默认密码


class Config:
    """配置管理类"""
    
    def __init__(self):
        self.config_dir = os.path.dirname(__file__)
        self.config_file = os.path.join(self.config_dir, "config.json")
        self.template_file = os.path.join(self.config_dir, "config_template.json")
        
        # 初始化配置
        self.wechat: WeChatSettings
        self.ai: AISettings
        self.database: DatabaseSettings
        self.reply: ReplySettings
        self.web: WebSettings
        
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            self.create_default_config()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载各项配置
            self.wechat = WeChatSettings(**data.get('wechat', {}))
            self.ai = AISettings(**data.get('ai', {}))
            self.database = DatabaseSettings(**data.get('database', {}))
            self.reply = ReplySettings(**data.get('reply', {}))
            self.web = WebSettings(**data.get('web', {}))
            
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            self.create_default_config()
            self.load_config()
    
    def save_config(self):
        """保存配置文件"""
        data = {
            'wechat': {
                'listen_list': self.wechat.listen_list,
                'listen_all': self.wechat.listen_all,
                'auto_reply': self.wechat.auto_reply,
                'reply_delay': self.wechat.reply_delay
            },
            'ai': {
                'api_key': self.ai.api_key,
                'base_url': self.ai.base_url,
                'model': self.ai.model,
                'max_tokens': self.ai.max_tokens,
                'temperature': self.ai.temperature,
                'enabled': self.ai.enabled
            },
            'database': {
                'faq_file': self.database.faq_file,
                'products_file': self.database.products_file,
                'similarity_threshold': self.database.similarity_threshold
            },
            'reply': {
                'priority_faq': self.reply.priority_faq,
                'priority_products': self.reply.priority_products,
                'use_ai_fallback': self.reply.use_ai_fallback,
                'default_reply': self.reply.default_reply
            },
            'web': {
                'host': self.web.host,
                'port': self.web.port,
                'debug': self.web.debug,
                'secret_key': self.web.secret_key,
                'password': self.web.password
            }
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"配置文件保存失败: {e}")
    
    def create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            'wechat': {
                'listen_list': [],
                'listen_all': False,
                'auto_reply': True,
                'reply_delay': 2
            },
            'ai': {
                'api_key': '',
                'base_url': 'https://api.siliconflow.cn/v1',
                'model': 'Qwen/Qwen2.5-7B-Instruct',
                'max_tokens': 1000,
                'temperature': 0.7,
                'enabled': True
            },
            'database': {
                'faq_file': 'data/faq.xlsx',
                'products_file': 'data/products.xlsx',
                'similarity_threshold': 0.7
            },
            'reply': {
                'priority_faq': True,
                'priority_products': True,
                'use_ai_fallback': True,
                'default_reply': ''
            },
            'web': {
                'host': '127.0.0.1',
                'port': 5000,
                'debug': False,
                'secret_key': 'wchat-secret-key',
                'password': 'admin123'
            }
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"创建默认配置文件失败: {e}")


# 全局配置实例
config = Config()
