2025-07-18 09:01:35,762 - main - INFO - 启动微信客服机器人...
2025-07-18 09:01:35,763 - main - INFO - 检查依赖和配置...
2025-07-18 09:01:35,763 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 09:01:35,764 - main - INFO - 依赖检查完成
2025-07-18 09:01:35,764 - main - INFO - ============================================================
2025-07-18 09:01:35,764 - main - INFO - 微信客服机器人状态
2025-07-18 09:01:35,765 - main - INFO - ============================================================
2025-07-18 09:01:35,766 - main - INFO - 监听列表: []
2025-07-18 09:01:35,766 - main - INFO - 自动回复: 启用
2025-07-18 09:01:35,767 - main - INFO - 回复延迟: 2秒
2025-07-18 09:01:35,768 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 09:01:35,769 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 09:01:35,770 - main - INFO - 相似度阈值: 0.7
2025-07-18 09:01:35,771 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 09:01:35,772 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 09:01:35,773 - main - INFO - API密钥: 已配置
2025-07-18 09:01:35,774 - main - INFO - ============================================================
2025-07-18 09:01:36,032 - main - INFO - 正在启动微信消息监听...
2025-07-18 09:01:41,633 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 09:01:41,636 - main - INFO - 💡 提示:
2025-07-18 09:01:41,638 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 09:01:41,642 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 09:01:41,654 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 09:01:58,814 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 09:01:59,001 - main - INFO - 程序已退出
2025-07-18 09:01:59,002 - main - INFO - 程序已退出
