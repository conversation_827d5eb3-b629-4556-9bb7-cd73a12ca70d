2025-07-18 09:01:36 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 09:01:36 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-18 09:01:41 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-18 09:01:41 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-18 09:01:41 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-18 09:01:41 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-18 09:01:41 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-18 09:01:41 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-18 09:01:41 [main] [INFO] [run.py:150]  💡 提示:
2025-07-18 09:01:41 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-18 09:01:41 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-18 09:01:41 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-18 09:01:58 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-18 09:01:59 [wechat_handler] [INFO] [wechat_handler.py:303]  停止监听微信消息
2025-07-18 09:01:59 [main] [INFO] [run.py:35]  程序已退出
2025-07-18 09:01:59 [main] [INFO] [run.py:176]  程序已退出
