import sys
import traceback

try:
    print("Python版本:", sys.version)
    print("开始基础测试...")
    
    # 测试基本导入
    print("测试基本模块...")
    import time
    import os
    print("基本模块OK")
    
    # 测试wxauto导入
    print("测试wxauto导入...")
    try:
        import wxauto
        print("wxauto导入成功")
        
        # 测试创建对象
        print("测试创建微信对象...")
        wx = wxauto.WeChat()
        print("微信对象创建成功")
        
        # 简单等待
        print("等待3秒...")
        time.sleep(3)
        
        # 测试属性
        print("测试获取昵称...")
        if hasattr(wx, 'nickname'):
            nickname = wx.nickname
            if nickname:
                print(f"成功！用户: {nickname}")
            else:
                print("昵称为空")
        else:
            print("没有nickname属性")
            
    except Exception as e:
        print(f"wxauto测试失败: {e}")
        traceback.print_exc()
    
    print("测试完成")
    
except Exception as e:
    print(f"基础测试失败: {e}")
    traceback.print_exc()

finally:
    input("按任意键退出...")
