2025-07-18 08:58:56,310 - main - INFO - 启动微信客服机器人...
2025-07-18 08:58:56,311 - main - INFO - 检查依赖和配置...
2025-07-18 08:58:56,313 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 08:58:56,313 - main - INFO - 依赖检查完成
2025-07-18 08:58:56,315 - main - INFO - ============================================================
2025-07-18 08:58:56,322 - main - INFO - 微信客服机器人状态
2025-07-18 08:58:56,323 - main - INFO - ============================================================
2025-07-18 08:58:56,324 - main - INFO - 监听列表: []
2025-07-18 08:58:56,325 - main - INFO - 自动回复: 启用
2025-07-18 08:58:56,326 - main - INFO - 回复延迟: 2秒
2025-07-18 08:58:56,327 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 08:58:56,328 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 08:58:56,329 - main - INFO - 相似度阈值: 0.7
2025-07-18 08:58:56,330 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 08:58:56,331 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 08:58:56,334 - main - INFO - API密钥: 已配置
2025-07-18 08:58:56,339 - main - INFO - ============================================================
2025-07-18 08:58:56,725 - main - INFO - 正在启动微信消息监听...
2025-07-18 08:59:02,465 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 08:59:02,466 - main - INFO - 💡 提示:
2025-07-18 08:59:02,468 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 08:59:02,469 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 08:59:02,471 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 08:59:31,751 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 08:59:46,890 - main - INFO - 启动微信客服机器人...
2025-07-18 08:59:46,891 - main - INFO - 检查依赖和配置...
2025-07-18 08:59:46,892 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 08:59:46,893 - main - INFO - 依赖检查完成
2025-07-18 08:59:46,894 - main - INFO - ============================================================
2025-07-18 08:59:46,895 - main - INFO - 微信客服机器人状态
2025-07-18 08:59:46,898 - main - INFO - ============================================================
2025-07-18 08:59:46,901 - main - INFO - 监听列表: []
2025-07-18 08:59:46,902 - main - INFO - 自动回复: 启用
2025-07-18 08:59:46,903 - main - INFO - 回复延迟: 2秒
2025-07-18 08:59:46,903 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 08:59:46,905 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 08:59:46,906 - main - INFO - 相似度阈值: 0.7
2025-07-18 08:59:46,907 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 08:59:46,908 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 08:59:46,909 - main - INFO - API密钥: 已配置
2025-07-18 08:59:46,910 - main - INFO - ============================================================
2025-07-18 08:59:47,170 - main - INFO - 正在启动微信消息监听...
2025-07-18 08:59:52,785 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 08:59:52,787 - main - INFO - 💡 提示:
2025-07-18 08:59:52,788 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 08:59:52,790 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 08:59:52,791 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 09:00:52,463 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 09:09:30,097 - main - INFO - 启动微信客服机器人...
2025-07-18 09:09:30,097 - main - INFO - 检查依赖和配置...
2025-07-18 09:09:30,099 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 09:09:30,099 - main - INFO - 依赖检查完成
2025-07-18 09:09:30,100 - main - INFO - ============================================================
2025-07-18 09:09:30,100 - main - INFO - 微信客服机器人状态
2025-07-18 09:09:30,101 - main - INFO - ============================================================
2025-07-18 09:09:30,102 - main - INFO - 监听列表: []
2025-07-18 09:09:30,102 - main - INFO - 自动回复: 启用
2025-07-18 09:09:30,103 - main - INFO - 回复延迟: 2秒
2025-07-18 09:09:30,104 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 09:09:30,104 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 09:09:30,105 - main - INFO - 相似度阈值: 0.7
2025-07-18 09:09:30,105 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 09:09:30,106 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 09:09:30,106 - main - INFO - API密钥: 已配置
2025-07-18 09:09:30,107 - main - INFO - ============================================================
2025-07-18 09:09:30,434 - main - INFO - 正在启动微信消息监听...
2025-07-18 09:09:36,204 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 09:09:36,204 - main - INFO - 💡 提示:
2025-07-18 09:09:36,205 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 09:09:36,205 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 09:09:36,206 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 09:10:46,696 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 09:10:51,708 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 09:10:51,709 - main - INFO - 程序已退出
2025-07-18 09:10:51,709 - main - INFO - 程序已退出
2025-07-18 09:21:26,309 - main - INFO - 启动微信客服机器人...
2025-07-18 09:21:26,309 - main - INFO - 检查依赖和配置...
2025-07-18 09:21:26,310 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 09:21:26,310 - main - INFO - 依赖检查完成
2025-07-18 09:21:26,311 - main - INFO - ============================================================
2025-07-18 09:21:26,311 - main - INFO - 微信客服机器人状态
2025-07-18 09:21:26,312 - main - INFO - ============================================================
2025-07-18 09:21:26,312 - main - INFO - 监听列表: []
2025-07-18 09:21:26,313 - main - INFO - 自动回复: 启用
2025-07-18 09:21:26,314 - main - INFO - 回复延迟: 2秒
2025-07-18 09:21:26,314 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 09:21:26,314 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 09:21:26,315 - main - INFO - 相似度阈值: 0.7
2025-07-18 09:21:26,315 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 09:21:26,316 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 09:21:26,316 - main - INFO - API密钥: 已配置
2025-07-18 09:21:26,317 - main - INFO - ============================================================
2025-07-18 09:21:26,593 - main - INFO - 正在启动微信消息监听...
2025-07-18 09:21:32,186 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 09:21:32,186 - main - INFO - 💡 提示:
2025-07-18 09:21:32,187 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 09:21:32,188 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 09:21:32,188 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 09:25:52,401 - main - INFO - 启动微信客服机器人...
2025-07-18 09:25:52,402 - main - INFO - 检查依赖和配置...
2025-07-18 09:25:52,403 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 09:25:52,404 - main - INFO - 依赖检查完成
2025-07-18 09:25:52,405 - main - INFO - ============================================================
2025-07-18 09:25:52,405 - main - INFO - 微信客服机器人状态
2025-07-18 09:25:52,406 - main - INFO - ============================================================
2025-07-18 09:25:52,407 - main - INFO - 监听列表: []
2025-07-18 09:25:52,408 - main - INFO - 自动回复: 启用
2025-07-18 09:25:52,409 - main - INFO - 回复延迟: 2秒
2025-07-18 09:25:52,410 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 09:25:52,411 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 09:25:52,412 - main - INFO - 相似度阈值: 0.7
2025-07-18 09:25:52,413 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 09:25:52,414 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 09:25:52,422 - main - INFO - API密钥: 已配置
2025-07-18 09:25:52,424 - main - INFO - ============================================================
2025-07-18 09:25:52,689 - main - INFO - 正在启动微信消息监听...
2025-07-18 09:25:58,009 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 09:25:58,011 - main - INFO - 💡 提示:
2025-07-18 09:25:58,011 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 09:25:58,014 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 09:25:58,015 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 09:28:11,324 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 09:41:33,707 - main - INFO - 启动微信客服机器人...
2025-07-18 09:41:33,708 - main - INFO - 检查依赖和配置...
2025-07-18 09:41:33,710 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 09:41:33,711 - main - INFO - 依赖检查完成
2025-07-18 09:41:33,712 - main - INFO - ============================================================
2025-07-18 09:41:33,713 - main - INFO - 微信客服机器人状态
2025-07-18 09:41:33,714 - main - INFO - ============================================================
2025-07-18 09:41:33,715 - main - INFO - 监听列表: []
2025-07-18 09:41:33,716 - main - INFO - 自动回复: 启用
2025-07-18 09:41:33,717 - main - INFO - 回复延迟: 2秒
2025-07-18 09:41:33,721 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 09:41:33,722 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 09:41:33,723 - main - INFO - 相似度阈值: 0.7
2025-07-18 09:41:33,724 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 09:41:33,725 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 09:41:33,726 - main - INFO - API密钥: 已配置
2025-07-18 09:41:33,727 - main - INFO - ============================================================
2025-07-18 09:41:33,997 - main - INFO - 正在启动微信消息监听...
2025-07-18 09:41:39,418 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 09:41:39,421 - main - INFO - 💡 提示:
2025-07-18 09:41:39,423 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 09:41:39,434 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 09:41:39,440 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 09:42:05,993 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 09:42:12,841 - main - INFO - 启动微信客服机器人...
2025-07-18 09:42:12,842 - main - INFO - 检查依赖和配置...
2025-07-18 09:42:12,843 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 09:42:12,844 - main - INFO - 依赖检查完成
2025-07-18 09:42:12,845 - main - INFO - ============================================================
2025-07-18 09:42:12,846 - main - INFO - 微信客服机器人状态
2025-07-18 09:42:12,847 - main - INFO - ============================================================
2025-07-18 09:42:12,848 - main - INFO - 监听列表: []
2025-07-18 09:42:12,849 - main - INFO - 自动回复: 启用
2025-07-18 09:42:12,852 - main - INFO - 回复延迟: 2秒
2025-07-18 09:42:12,854 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 09:42:12,855 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 09:42:12,857 - main - INFO - 相似度阈值: 0.7
2025-07-18 09:42:12,859 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 09:42:12,860 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 09:42:12,860 - main - INFO - API密钥: 已配置
2025-07-18 09:42:12,861 - main - INFO - ============================================================
2025-07-18 09:42:13,111 - main - INFO - 正在启动微信消息监听...
2025-07-18 09:42:18,495 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 09:42:18,497 - main - INFO - 💡 提示:
2025-07-18 09:42:18,498 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 09:42:18,499 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 09:42:18,501 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 09:42:48,521 - main - INFO - 收到退出信号，正在关闭程序...
