2025-07-18 08:58:56,310 - main - INFO - 启动微信客服机器人...
2025-07-18 08:58:56,311 - main - INFO - 检查依赖和配置...
2025-07-18 08:58:56,313 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 08:58:56,313 - main - INFO - 依赖检查完成
2025-07-18 08:58:56,315 - main - INFO - ============================================================
2025-07-18 08:58:56,322 - main - INFO - 微信客服机器人状态
2025-07-18 08:58:56,323 - main - INFO - ============================================================
2025-07-18 08:58:56,324 - main - INFO - 监听列表: []
2025-07-18 08:58:56,325 - main - INFO - 自动回复: 启用
2025-07-18 08:58:56,326 - main - INFO - 回复延迟: 2秒
2025-07-18 08:58:56,327 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 08:58:56,328 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 08:58:56,329 - main - INFO - 相似度阈值: 0.7
2025-07-18 08:58:56,330 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 08:58:56,331 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 08:58:56,334 - main - INFO - API密钥: 已配置
2025-07-18 08:58:56,339 - main - INFO - ============================================================
2025-07-18 08:58:56,725 - main - INFO - 正在启动微信消息监听...
2025-07-18 08:59:02,465 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 08:59:02,466 - main - INFO - 💡 提示:
2025-07-18 08:59:02,468 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 08:59:02,469 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 08:59:02,471 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 08:59:31,751 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 08:59:46,890 - main - INFO - 启动微信客服机器人...
2025-07-18 08:59:46,891 - main - INFO - 检查依赖和配置...
2025-07-18 08:59:46,892 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 08:59:46,893 - main - INFO - 依赖检查完成
2025-07-18 08:59:46,894 - main - INFO - ============================================================
2025-07-18 08:59:46,895 - main - INFO - 微信客服机器人状态
2025-07-18 08:59:46,898 - main - INFO - ============================================================
2025-07-18 08:59:46,901 - main - INFO - 监听列表: []
2025-07-18 08:59:46,902 - main - INFO - 自动回复: 启用
2025-07-18 08:59:46,903 - main - INFO - 回复延迟: 2秒
2025-07-18 08:59:46,903 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 08:59:46,905 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 08:59:46,906 - main - INFO - 相似度阈值: 0.7
2025-07-18 08:59:46,907 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 08:59:46,908 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 08:59:46,909 - main - INFO - API密钥: 已配置
2025-07-18 08:59:46,910 - main - INFO - ============================================================
2025-07-18 08:59:47,170 - main - INFO - 正在启动微信消息监听...
2025-07-18 08:59:52,785 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 08:59:52,787 - main - INFO - 💡 提示:
2025-07-18 08:59:52,788 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 08:59:52,790 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 08:59:52,791 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 09:00:52,463 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 09:09:30,097 - main - INFO - 启动微信客服机器人...
2025-07-18 09:09:30,097 - main - INFO - 检查依赖和配置...
2025-07-18 09:09:30,099 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 09:09:30,099 - main - INFO - 依赖检查完成
2025-07-18 09:09:30,100 - main - INFO - ============================================================
2025-07-18 09:09:30,100 - main - INFO - 微信客服机器人状态
2025-07-18 09:09:30,101 - main - INFO - ============================================================
2025-07-18 09:09:30,102 - main - INFO - 监听列表: []
2025-07-18 09:09:30,102 - main - INFO - 自动回复: 启用
2025-07-18 09:09:30,103 - main - INFO - 回复延迟: 2秒
2025-07-18 09:09:30,104 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 09:09:30,104 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 09:09:30,105 - main - INFO - 相似度阈值: 0.7
2025-07-18 09:09:30,105 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 09:09:30,106 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 09:09:30,106 - main - INFO - API密钥: 已配置
2025-07-18 09:09:30,107 - main - INFO - ============================================================
2025-07-18 09:09:30,434 - main - INFO - 正在启动微信消息监听...
2025-07-18 09:09:36,204 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 09:09:36,204 - main - INFO - 💡 提示:
2025-07-18 09:09:36,205 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 09:09:36,205 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 09:09:36,206 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-18 09:10:46,696 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 09:10:51,708 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-18 09:10:51,709 - main - INFO - 程序已退出
2025-07-18 09:10:51,709 - main - INFO - 程序已退出
2025-07-18 09:21:26,309 - main - INFO - 启动微信客服机器人...
2025-07-18 09:21:26,309 - main - INFO - 检查依赖和配置...
2025-07-18 09:21:26,310 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-18 09:21:26,310 - main - INFO - 依赖检查完成
2025-07-18 09:21:26,311 - main - INFO - ============================================================
2025-07-18 09:21:26,311 - main - INFO - 微信客服机器人状态
2025-07-18 09:21:26,312 - main - INFO - ============================================================
2025-07-18 09:21:26,312 - main - INFO - 监听列表: []
2025-07-18 09:21:26,313 - main - INFO - 自动回复: 启用
2025-07-18 09:21:26,314 - main - INFO - 回复延迟: 2秒
2025-07-18 09:21:26,314 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-18 09:21:26,314 - main - INFO - 产品文件: data/products.xlsx
2025-07-18 09:21:26,315 - main - INFO - 相似度阈值: 0.7
2025-07-18 09:21:26,315 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-18 09:21:26,316 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-18 09:21:26,316 - main - INFO - API密钥: 已配置
2025-07-18 09:21:26,317 - main - INFO - ============================================================
2025-07-18 09:21:26,593 - main - INFO - 正在启动微信消息监听...
2025-07-18 09:21:32,186 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-18 09:21:32,186 - main - INFO - 💡 提示:
2025-07-18 09:21:32,187 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-18 09:21:32,188 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-18 09:21:32,188 - main - INFO -    - 确保微信PC版保持登录状态
