#!/usr/bin/env python3
"""
直接修复发送消息问题
使用最简单的方法
"""
import time
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_current_send_method():
    """测试当前的发送方法"""
    print("=" * 50)
    print("测试当前发送方法")
    print("=" * 50)
    
    try:
        import wxauto
        
        print("连接微信...")
        wx = wxauto.WeChat()
        time.sleep(3)
        
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"✅ 微信连接成功，用户: {wx.nickname}")
            
            # 测试发送消息
            test_message = "测试发送消息功能"
            print(f"尝试发送: {test_message}")
            
            try:
                # 方法1: 直接SendMsg
                wx.SendMsg(msg=test_message)
                print("✅ 方法1成功: 直接SendMsg")
                return True
            except Exception as e:
                print(f"❌ 方法1失败: {e}")
                
                # 方法2: 尝试带参数
                try:
                    wx.SendMsg(test_message)
                    print("✅ 方法2成功: SendMsg(message)")
                    return True
                except Exception as e2:
                    print(f"❌ 方法2失败: {e2}")
                    
                    # 方法3: 尝试键盘模拟
                    try:
                        success = send_by_keyboard_simple(wx, test_message)
                        if success:
                            print("✅ 方法3成功: 键盘模拟")
                            return True
                        else:
                            print("❌ 方法3失败: 键盘模拟")
                    except Exception as e3:
                        print(f"❌ 方法3异常: {e3}")
            
            return False
        else:
            print("❌ 微信连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def send_by_keyboard_simple(wx_instance, message):
    """简单的键盘模拟发送"""
    try:
        # 安装依赖
        try:
            import pyautogui
            import pyperclip
        except ImportError:
            print("安装pyautogui...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyautogui", "pyperclip"])
            import pyautogui
            import pyperclip
        
        print("使用键盘模拟发送消息...")
        
        # 复制消息到剪贴板
        pyperclip.copy(message)
        
        # 等待一下确保剪贴板更新
        time.sleep(0.5)
        
        # 粘贴消息
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.5)
        
        # 发送消息
        pyautogui.press('enter')
        time.sleep(0.5)
        
        return True
        
    except Exception as e:
        print(f"键盘模拟失败: {e}")
        return False

def create_simple_send_wrapper():
    """创建简单的发送包装器"""
    wrapper_code = '''
def safe_send_msg(wx_instance, message, chat_name=None):
    """安全发送消息的包装器"""
    import time
    
    try:
        # 如果指定了聊天对象，先切换
        if chat_name:
            try:
                wx_instance.ChatWith(chat_name)
                time.sleep(0.5)
            except Exception as e:
                print(f"切换聊天失败: {e}")
        
        # 尝试发送消息
        try:
            wx_instance.SendMsg(msg=message)
            print(f"发送成功: {message[:30]}...")
            return True
        except Exception as e:
            print(f"SendMsg失败: {e}")
            
            # 尝试键盘模拟
            try:
                import pyautogui
                import pyperclip
                
                pyperclip.copy(message)
                time.sleep(0.3)
                pyautogui.hotkey('ctrl', 'v')
                time.sleep(0.3)
                pyautogui.press('enter')
                time.sleep(0.3)
                
                print(f"键盘模拟发送成功: {message[:30]}...")
                return True
            except Exception as e2:
                print(f"键盘模拟也失败: {e2}")
                return False
                
    except Exception as e:
        print(f"发送消息异常: {e}")
        return False
'''
    
    with open("src/utils/simple_send_wrapper.py", 'w', encoding='utf-8') as f:
        f.write(wrapper_code)
    
    print("✅ 创建了简单发送包装器")

def update_wechat_handler_simple():
    """简单更新微信处理器"""
    handler_file = Path("src/bot/wechat_handler.py")
    
    if not handler_file.exists():
        print("❌ 找不到wechat_handler.py文件")
        return False
    
    # 读取当前内容
    with open(handler_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经导入了包装器
    if "from src.utils.simple_send_wrapper import safe_send_msg" in content:
        print("✅ 微信处理器已使用简单发送包装器")
        return True
    
    # 添加导入
    import_line = "from src.utils.safe_message_sender import enhanced_send_message"
    new_import = "from src.utils.simple_send_wrapper import safe_send_msg"
    
    if import_line in content:
        content = content.replace(import_line, new_import)
    else:
        # 在其他导入后添加
        logger_import = "from src.utils.logger import get_logger"
        if logger_import in content:
            content = content.replace(logger_import, f"{logger_import}\nfrom src.utils.simple_send_wrapper import safe_send_msg")
    
    # 替换发送方法调用
    old_call = "success = enhanced_send_message(self.wx, reply, chat_name)"
    new_call = "success = safe_send_msg(self.wx, reply, chat_name)"
    
    if old_call in content:
        content = content.replace(old_call, new_call)
    
    # 替换手动发送调用
    old_manual = "success = enhanced_send_message(self.wx, message, chat_id)"
    new_manual = "success = safe_send_msg(self.wx, message, chat_id)"
    
    if old_manual in content:
        content = content.replace(old_manual, new_manual)
    
    # 写入修复后的内容
    with open(handler_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 微信处理器已更新为使用简单发送包装器")
    return True

def main():
    """主修复流程"""
    print("=" * 60)
    print("直接修复发送消息问题")
    print("=" * 60)
    
    print("请确保:")
    print("1. 微信PC版已启动并登录")
    print("2. 已打开一个聊天窗口")
    print("3. 聊天窗口处于活跃状态")
    print()
    
    input("准备好后按回车键开始...")
    
    # 1. 测试当前发送方法
    print("\n1. 测试当前发送方法...")
    test_result = test_current_send_method()
    
    # 2. 创建简单包装器
    print("\n2. 创建简单发送包装器...")
    create_simple_send_wrapper()
    
    # 3. 更新微信处理器
    print("\n3. 更新微信处理器...")
    update_wechat_handler_simple()
    
    # 总结
    print("\n" + "=" * 60)
    print("修复结果")
    print("=" * 60)
    
    if test_result:
        print("✅ 发送功能测试通过")
    else:
        print("⚠️  发送功能测试未完全通过，但已创建备用方案")
    
    print("✅ 已创建简单发送包装器")
    print("✅ 已更新微信处理器")
    
    print("\n🎉 修复完成！")
    print("\n现在可以重新启动WChat机器人:")
    print("python run.py")
    
    print("\n💡 修复说明:")
    print("- 创建了简单的发送消息包装器")
    print("- 当原始SendMsg失败时自动使用键盘模拟")
    print("- 避免了复杂的控件识别问题")
    
    print("\n" + "=" * 60)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
