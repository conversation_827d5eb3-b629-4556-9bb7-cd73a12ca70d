2025-07-17 13:38:45,088 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 13:38:45,464 - web_app - INFO - Web应用初始化完成
2025-07-17 13:38:46,543 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 13:38:46,794 - web_app - INFO - Web应用初始化完成
2025-07-17 13:38:58,383 - web_app - INFO - 配置对象类型: <class 'config.Config'>
2025-07-17 13:38:58,383 - web_app - INFO - 系统配置: WeChatSettings(listen_list=[], listen_all=True, auto_reply=True, reply_delay=2, voice_to_text=True, voice_reply_enabled=True)
2025-07-17 13:38:58,386 - web_app - INFO - 系统配置类型: <class 'config.WeChatSettings'>
2025-07-17 13:38:58,387 - web_app - INFO - listen_all属性: True
2025-07-17 13:38:58,387 - web_app - INFO - listen_all值: True
2025-07-17 13:39:04,130 - web_app - INFO - 配置对象类型: <class 'config.Config'>
2025-07-17 13:39:04,130 - web_app - INFO - 系统配置: WeChatSettings(listen_list=[], listen_all=True, auto_reply=True, reply_delay=2, voice_to_text=True, voice_reply_enabled=True)
2025-07-17 13:39:04,131 - web_app - INFO - 系统配置类型: <class 'config.WeChatSettings'>
2025-07-17 13:39:04,132 - web_app - INFO - listen_all属性: True
2025-07-17 13:39:04,132 - web_app - INFO - listen_all值: True
2025-07-17 13:40:16,486 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 13:40:16,742 - web_app - INFO - Web应用初始化完成
2025-07-17 13:40:17,759 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 13:40:18,033 - web_app - INFO - Web应用初始化完成
2025-07-17 13:41:17,472 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 13:41:17,748 - web_app - INFO - Web应用初始化完成
2025-07-17 13:41:18,774 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 13:41:19,039 - web_app - INFO - Web应用初始化完成
2025-07-17 13:44:51,288 - web_app - INFO - 使用增强回复引擎和数据读取器
2025-07-17 13:44:51,637 - web_app - INFO - Web应用初始化完成
