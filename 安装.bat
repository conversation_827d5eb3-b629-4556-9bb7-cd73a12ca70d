@echo off
chcp 65001 >nul
echo ========================================
echo     wchat微信智能客服机器人安装程序
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境！
    echo 💡 请先安装Python 3.8或更高版本
    echo 📥 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 安装Python依赖包...
cd wchat

REM 首先尝试升级pip
echo 🔄 升级pip...
python -m pip install --upgrade pip

REM 使用UTF-8编码安装依赖包
echo 📥 安装依赖包...
python -m pip install -r requirements.txt --no-cache-dir

if errorlevel 1 (
    echo ❌ 依赖包安装失败！
    echo 💡 尝试使用国内镜像源...
    python -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --no-cache-dir
    if errorlevel 1 (
        echo ❌ 使用镜像源仍然失败！
        echo 🔧 请手动执行: cd wchat && python -m pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo ✅ 依赖包安装完成

echo.
echo 🎉 安装完成！
echo.
echo 📋 下一步操作:
echo    1. 编辑 wchat/config/config.json 配置文件
echo    2. 设置AI API密钥和其他配置
echo    3. 运行 启动.bat 启动程序
echo.
pause
