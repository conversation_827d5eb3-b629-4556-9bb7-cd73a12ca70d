"""
Web配置界面启动文件
"""
import os
import sys
import webbrowser
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from src.web.app import app
from config import config
from src.utils.logger import get_logger

logger = get_logger("web_config")


def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    url = f"http://{config.web.host}:{config.web.port}"
    try:
        webbrowser.open(url)
        logger.info(f"已打开浏览器: {url}")
    except Exception as e:
        logger.error(f"打开浏览器失败: {e}")


def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    微信客服机器人 Web配置界面                    ║
    ║                                                              ║
    ║  访问地址: http://{}:{}                        ║
    ║  默认密码: admin123                                          ║
    ║                                                              ║
    ║  功能:                                                       ║
    ║  • 基础配置管理                                              ║
    ║  • FAQ库管理                                                ║
    ║  • 产品库管理                                               ║
    ║  • 系统状态监控                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """.format(config.web.host, config.web.port)
    print(banner)


def main():
    """主函数"""
    try:
        print_banner()
        logger.info("启动Web配置界面...")
        
        # 检查必要目录
        data_dir = current_dir / "data"
        if not data_dir.exists():
            data_dir.mkdir(parents=True)
            logger.info("创建data目录")
        
        # 在新线程中打开浏览器
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # 启动Flask应用
        logger.info(f"Web服务器启动在 http://{config.web.host}:{config.web.port}")
        logger.info("按 Ctrl+C 退出")
        
        app.run(
            host=config.web.host,
            port=config.web.port,
            debug=config.web.debug,
            use_reloader=False  # 避免重复启动
        )
        
    except KeyboardInterrupt:
        logger.info("用户中断，正在退出...")
    except Exception as e:
        logger.error(f"启动失败: {e}")
    finally:
        logger.info("Web配置界面已退出")


if __name__ == "__main__":
    main()
