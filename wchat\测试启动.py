#!/usr/bin/env python3
"""
简单的启动测试脚本
用于诊断启动问题
"""
import sys
import os
import time

def test_basic():
    """基础测试"""
    print("=" * 50)
    print("WChat启动测试")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print(f"脚本位置: {__file__}")
    
    print("\n测试Python基础功能...")
    try:
        import json
        print("✅ json模块正常")
    except Exception as e:
        print(f"❌ json模块错误: {e}")
    
    try:
        import time
        print("✅ time模块正常")
    except Exception as e:
        print(f"❌ time模块错误: {e}")
    
    print("\n测试依赖包...")
    
    # 测试wxauto
    try:
        import wxauto
        print("✅ wxauto已安装")
    except ImportError:
        print("❌ wxauto未安装")
        print("请运行: pip install wxauto")
    except Exception as e:
        print(f"❌ wxauto错误: {e}")
    
    # 测试flask
    try:
        import flask
        print("✅ flask已安装")
    except ImportError:
        print("❌ flask未安装")
        print("请运行: pip install flask")
    except Exception as e:
        print(f"❌ flask错误: {e}")
    
    # 测试pandas
    try:
        import pandas
        print("✅ pandas已安装")
    except ImportError:
        print("❌ pandas未安装")
        print("请运行: pip install pandas")
    except Exception as e:
        print(f"❌ pandas错误: {e}")
    
    print("\n测试项目结构...")
    
    # 检查关键目录
    dirs_to_check = ['src', 'config', 'data']
    for dir_name in dirs_to_check:
        if os.path.exists(dir_name):
            print(f"✅ {dir_name}目录存在")
        else:
            print(f"❌ {dir_name}目录不存在")
    
    # 检查关键文件
    files_to_check = [
        'config/config.json',
        'src/bot/wechat_handler.py',
        'run.py'
    ]
    for file_name in files_to_check:
        if os.path.exists(file_name):
            print(f"✅ {file_name}文件存在")
        else:
            print(f"❌ {file_name}文件不存在")
    
    print("\n测试导入项目模块...")
    
    # 添加项目路径
    sys.path.insert(0, os.getcwd())
    
    try:
        from config import config
        print("✅ 配置模块导入成功")
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
    
    try:
        from src.utils.logger import get_logger
        print("✅ 日志模块导入成功")
    except Exception as e:
        print(f"❌ 日志模块导入失败: {e}")
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        print("✅ 微信处理器模块导入成功")
    except Exception as e:
        print(f"❌ 微信处理器模块导入失败: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)
    
    # 等待用户输入
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        test_basic()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
