#!/bin/bash

echo "========================================"
echo "   wchat微信智能客服机器人安装程序"
echo "========================================"
echo

echo "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 未检测到Python3环境！"
    echo "💡 请先安装Python 3.8或更高版本"
    exit 1
fi

echo "✅ Python环境检查通过"

echo
echo "📦 安装Python依赖包..."
cd wchat
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ 依赖包安装失败！"
    echo "💡 请检查网络连接"
    exit 1
fi

echo "✅ 依赖包安装完成"

echo
echo "🎉 安装完成！"
echo
echo "📋 下一步操作:"
echo "   1. 编辑 wchat/config/config.json 配置文件"
echo "   2. 设置AI API密钥和其他配置"
echo "   3. 运行 ./start.sh 启动程序"
echo
