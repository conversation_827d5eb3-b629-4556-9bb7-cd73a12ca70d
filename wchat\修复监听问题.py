#!/usr/bin/env python3
"""
修复wxauto监听问题
解决 'cannot access local variable length' 错误
"""
import sys
import os
import time
from pathlib import Path

def patch_wxauto_length_error():
    """修复wxauto的length变量错误"""
    try:
        import wxauto
        from wxauto import wxauto as wxauto_module
        
        # 保存原始的GetNewMessage方法
        original_get_new_message = wxauto_module.WeChat.GetNewMessage
        
        def patched_get_new_message(self):
            """修复后的GetNewMessage方法"""
            try:
                # 尝试调用原始方法
                return original_get_new_message(self)
            except Exception as e:
                error_str = str(e)
                if "cannot access local variable 'length'" in error_str:
                    # 这是wxauto内部的变量作用域错误，返回空列表
                    print(f"[修复] 捕获wxauto length错误，返回空消息列表")
                    return []
                else:
                    # 其他错误继续抛出
                    raise e
        
        # 替换方法
        wxauto_module.WeChat.GetNewMessage = patched_get_new_message
        print("✅ wxauto GetNewMessage补丁应用成功")
        return True
        
    except Exception as e:
        print(f"❌ 应用wxauto补丁失败: {e}")
        return False

def patch_wechat_handler():
    """修复微信处理器中的错误处理"""
    handler_file = Path("src/bot/wechat_handler.py")
    
    if not handler_file.exists():
        print("❌ 找不到wechat_handler.py文件")
        return False
    
    # 读取当前内容
    with open(handler_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经修复
    if "cannot access local variable 'length'" in content:
        print("✅ 微信处理器已包含length错误处理")
        return True
    
    # 添加对length错误的特殊处理
    old_pattern = '''                        if ("NoneType" in error_msg and "get_new_msgs" in error_msg):'''
    
    new_pattern = '''                        if ("cannot access local variable 'length'" in error_msg):
                            # wxauto内部变量作用域错误，跳过这次获取
                            logger.debug("检测到wxauto length变量错误，跳过本次消息获取")
                            msgs = None
                        elif ("NoneType" in error_msg and "get_new_msgs" in error_msg):'''
    
    if old_pattern in content:
        content = content.replace(old_pattern, new_pattern)
        
        # 写入修复后的内容
        with open(handler_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 微信处理器length错误处理已添加")
        return True
    else:
        print("⚠️  未找到预期的错误处理代码段")
        return False

def test_fixed_connection():
    """测试修复后的连接"""
    print("\n🔍 测试修复后的微信连接...")
    
    try:
        # 应用补丁
        if not patch_wxauto_length_error():
            return False
        
        import wxauto
        
        print("正在连接微信...")
        wx = wxauto.WeChat()
        
        print("等待连接稳定...")
        time.sleep(3)
        
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"✅ 微信连接成功，用户: {wx.nickname}")
            
            # 测试GetNewMessage方法
            print("测试GetNewMessage方法...")
            try:
                msgs = wx.GetNewMessage()
                print(f"✅ GetNewMessage成功，返回 {len(msgs) if msgs else 0} 条消息")
                return True
            except Exception as e:
                print(f"❌ GetNewMessage仍然失败: {e}")
                return False
        else:
            print("❌ 无法获取用户信息")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def create_alternative_message_getter():
    """创建替代的消息获取方法"""
    print("\n🔧 创建替代消息获取方法...")
    
    alternative_code = '''
def safe_get_new_message(wx_instance):
    """安全的消息获取方法，避免wxauto内部错误"""
    try:
        # 方法1: 尝试GetNewMessage
        try:
            msgs = wx_instance.GetNewMessage()
            if msgs:
                return msgs
        except Exception as e:
            if "cannot access local variable 'length'" in str(e):
                pass  # 忽略length错误
            else:
                raise e
        
        # 方法2: 尝试GetNextNewMessage
        try:
            next_msg = wx_instance.GetNextNewMessage()
            if next_msg and isinstance(next_msg, dict):
                return [next_msg] if next_msg.get('content') else []
        except Exception:
            pass
        
        # 方法3: 尝试GetAllMessage并取最新的
        try:
            all_msgs = wx_instance.GetAllMessage()
            if all_msgs and len(all_msgs) > 0:
                # 只返回最新的几条消息
                return all_msgs[-3:] if len(all_msgs) > 3 else all_msgs
        except Exception:
            pass
        
        # 如果所有方法都失败，返回空列表
        return []
        
    except Exception as e:
        print(f"安全消息获取失败: {e}")
        return []
'''
    
    with open("src/utils/safe_message_getter.py", 'w', encoding='utf-8') as f:
        f.write(alternative_code)
    
    print("✅ 替代消息获取方法已创建")

def update_wechat_handler_to_use_safe_getter():
    """更新微信处理器使用安全的消息获取方法"""
    handler_file = Path("src/bot/wechat_handler.py")
    
    if not handler_file.exists():
        print("❌ 找不到wechat_handler.py文件")
        return False
    
    # 读取当前内容
    with open(handler_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经导入了安全获取方法
    if "from src.utils.safe_message_getter import safe_get_new_message" in content:
        print("✅ 微信处理器已使用安全消息获取方法")
        return True
    
    # 添加导入
    import_line = "from src.utils.logger import get_logger"
    new_import = "from src.utils.logger import get_logger\nfrom src.utils.safe_message_getter import safe_get_new_message"
    
    if import_line in content:
        content = content.replace(import_line, new_import)
    
    # 替换消息获取调用
    old_call = "msgs = self.wx.GetNewMessage()"
    new_call = "msgs = safe_get_new_message(self.wx)"
    
    if old_call in content:
        content = content.replace(old_call, new_call)
        
        # 写入修复后的内容
        with open(handler_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 微信处理器已更新为使用安全消息获取方法")
        return True
    else:
        print("⚠️  未找到预期的消息获取调用")
        return False

def main():
    """主修复流程"""
    print("=" * 60)
    print("wxauto监听问题修复工具")
    print("=" * 60)
    
    print("正在修复 'cannot access local variable length' 错误...")
    print()
    
    # 1. 修复wxauto库
    print("1. 修复wxauto库...")
    patch_wxauto_length_error()
    
    # 2. 修复微信处理器
    print("\n2. 修复微信处理器...")
    patch_wechat_handler()
    
    # 3. 创建替代方法
    print("\n3. 创建替代消息获取方法...")
    create_alternative_message_getter()
    
    # 4. 更新处理器使用替代方法
    print("\n4. 更新微信处理器...")
    update_wechat_handler_to_use_safe_getter()
    
    # 5. 测试修复效果
    print("\n5. 测试修复效果...")
    if test_fixed_connection():
        print("\n🎉 修复成功！")
        print("\n现在可以重新启动WChat机器人:")
        print("python run.py")
    else:
        print("\n❌ 修复后仍有问题")
        print("建议:")
        print("1. 重启微信PC版")
        print("2. 更新wxauto版本")
        print("3. 检查微信版本兼容性")
    
    print("\n" + "=" * 60)
    print("修复完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
