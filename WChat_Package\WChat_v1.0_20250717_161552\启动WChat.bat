@echo off
chcp 65001 >nul
title WChat微信客服机器人
echo ========================================
echo     WChat微信客服机器人
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python环境未找到！
    echo 请先运行"安装.bat"安装依赖
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 🔍 检查依赖包...
python -c "import wxauto" >nul 2>&1
if errorlevel 1 (
    echo ❌ 依赖包未安装！
    echo 请先运行"安装.bat"安装依赖
    pause
    exit /b 1
)

echo ✅ 依赖包正常

echo.
echo 🚀 启动WChat机器人...
echo 💡 请确保微信PC版已启动并登录
echo.
python run.py

echo.
echo 程序已退出
pause
