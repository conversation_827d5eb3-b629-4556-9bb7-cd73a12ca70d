#!/usr/bin/env python3
"""
WChat安全启动程序
专门处理微信连接问题的启动方式
"""
import os
import sys
import time
import psutil
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_wechat_running():
    """检查微信是否运行"""
    for proc in psutil.process_iter(['name']):
        try:
            if 'wechat' in proc.info['name'].lower():
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return False

def test_basic_connection():
    """测试基本微信连接"""
    try:
        from wxauto import WeChat
        print("正在测试微信连接...")
        
        wx = WeChat()
        time.sleep(3)  # 等待连接稳定
        
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"✅ 微信连接成功，用户: {wx.nickname}")
            return wx
        else:
            print("❌ 微信连接失败")
            return None
    except Exception as e:
        print(f"❌ 微信连接异常: {e}")
        return None

def safe_start_wechat_handler():
    """安全启动微信处理器"""
    try:
        from src.bot.wechat_handler import WeChatHandler
        from src.utils.logger import get_logger
        
        logger = get_logger("safe_start")
        
        print("正在创建微信处理器...")
        handler = WeChatHandler()
        
        print("正在初始化微信连接（增强模式）...")
        # 使用更多重试次数
        if handler.initialize_wechat(max_retries=5):
            print("✅ 微信初始化成功")
            
            print("正在启动消息监听...")
            handler.start_listening()
            
            if handler.running:
                print("🎉 WChat机器人启动成功！")
                print("\n状态信息:")
                print(f"  - 微信用户: {handler.wx.nickname if handler.wx else '未知'}")
                print(f"  - 监听状态: {'运行中' if handler.running else '已停止'}")
                print(f"  - 配置文件: config/config.json")
                
                print("\n💡 使用提示:")
                print("  - 按 Ctrl+C 退出程序")
                print("  - 保持微信PC版登录状态")
                print("  - 可运行 python web_config.py 打开配置界面")
                
                return handler
            else:
                print("❌ 消息监听启动失败")
                return None
        else:
            print("❌ 微信初始化失败")
            return None
            
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("          WChat安全启动程序")
    print("=" * 60)
    
    # 检查微信进程
    print("🔍 检查微信状态...")
    if not check_wechat_running():
        print("❌ 微信PC版未运行")
        print("请先启动微信PC版并登录，然后重新运行此程序")
        input("按回车键退出...")
        return
    
    print("✅ 微信PC版正在运行")
    
    # 测试基本连接
    print("\n🔍 测试微信连接...")
    wx = test_basic_connection()
    if not wx:
        print("❌ 微信连接测试失败")
        print("\n可能的解决方案:")
        print("1. 重启微信PC版")
        print("2. 确保微信已完全登录")
        print("3. 运行: python 修复微信连接.py")
        input("按回车键退出...")
        return
    
    # 启动机器人
    print("\n🚀 启动WChat机器人...")
    handler = safe_start_wechat_handler()
    
    if handler:
        try:
            # 主循环
            print("\n✅ 程序运行中...")
            while handler.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n收到退出信号...")
        finally:
            print("正在关闭程序...")
            if handler:
                handler.stop_listening()
            print("程序已退出")
    else:
        print("\n❌ 启动失败！")
        print("\n故障排除建议:")
        print("1. 运行: python 修复微信连接.py")
        print("2. 运行: python diagnose_wechat_issue.py")
        print("3. 检查config/config.json配置")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
