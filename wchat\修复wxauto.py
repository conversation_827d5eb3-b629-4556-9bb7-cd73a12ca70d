#!/usr/bin/env python3
"""
修复wxauto兼容性问题
"""
import sys
import os
import time
import traceback

def patch_wxauto():
    """修补wxauto库的兼容性问题"""
    try:
        import wxauto
        from wxauto import wxauto as wxauto_module
        
        # 保存原始的_show方法
        original_show = wxauto_module.WeChat._show
        
        def patched_show(self):
            """修补后的_show方法，跳过SetWindowPos调用"""
            try:
                # 尝试原始方法
                original_show(self)
            except Exception as e:
                if "SetWindowPos" in str(e) or "无效的窗口句柄" in str(e):
                    print(f"跳过SetWindowPos错误: {e}")
                    # 跳过窗口操作，直接继续
                    pass
                else:
                    raise e
        
        # 替换方法
        wxauto_module.WeChat._show = patched_show
        print("wxauto补丁应用成功")
        return True
        
    except Exception as e:
        print(f"应用wxauto补丁失败: {e}")
        return False

def test_patched_wxauto():
    """测试修补后的wxauto"""
    print("=" * 50)
    print("测试修补后的wxauto")
    print("=" * 50)
    
    try:
        # 应用补丁
        if not patch_wxauto():
            return False
        
        import wxauto
        print("正在创建微信对象...")
        
        # 创建微信对象
        wx = wxauto.WeChat()
        print("微信对象创建成功")
        
        # 等待
        print("等待连接稳定...")
        time.sleep(5)
        
        # 测试基本功能
        print("测试基本功能...")
        
        # 检查属性
        if hasattr(wx, 'nickname'):
            try:
                nickname = wx.nickname
                if nickname:
                    print(f"✅ 连接成功！用户: {nickname}")
                    return True
                else:
                    print("⚠️  微信对象创建成功，但无法获取昵称")
            except Exception as e:
                print(f"⚠️  获取昵称时出错: {e}")
        
        # 尝试其他方法
        try:
            current_chat = wx.CurrentChat()
            print(f"当前聊天: {current_chat}")
        except Exception as e:
            print(f"CurrentChat方法测试: {e}")
        
        print("✅ 基本连接成功（部分功能可能受限）")
        return True
        
    except Exception as e:
        print(f"❌ 修补后仍然失败: {e}")
        traceback.print_exc()
        return False

def try_alternative_wxauto_versions():
    """尝试不同版本的wxauto"""
    print("\n" + "=" * 50)
    print("尝试不同版本的wxauto")
    print("=" * 50)
    
    versions_to_try = [
        "********.6",
        "********.5", 
        "********.4",
        "********.3"
    ]
    
    import subprocess
    
    for version in versions_to_try:
        print(f"\n尝试版本 {version}...")
        try:
            # 卸载当前版本
            subprocess.run([sys.executable, "-m", "pip", "uninstall", "wxauto", "-y"], 
                         capture_output=True, check=True)
            
            # 安装指定版本
            result = subprocess.run([sys.executable, "-m", "pip", "install", f"wxauto=={version}"], 
                                  capture_output=True, check=True)
            
            print(f"版本 {version} 安装成功")
            
            # 测试这个版本
            try:
                # 重新导入
                if 'wxauto' in sys.modules:
                    del sys.modules['wxauto']
                
                import wxauto
                wx = wxauto.WeChat()
                time.sleep(3)
                
                if hasattr(wx, 'nickname') and wx.nickname:
                    print(f"✅ 版本 {version} 工作正常！用户: {wx.nickname}")
                    return version
                else:
                    print(f"⚠️  版本 {version} 部分工作")
                    
            except Exception as e:
                print(f"❌ 版本 {version} 测试失败: {e}")
                continue
                
        except subprocess.CalledProcessError as e:
            print(f"❌ 版本 {version} 安装失败")
            continue
    
    print("❌ 所有版本都无法正常工作")
    return None

def main():
    """主函数"""
    print("wxauto兼容性修复工具")
    print("=" * 50)
    
    print("请确保:")
    print("1. 微信PC版已启动并登录")
    print("2. 微信窗口可见")
    print("3. 以管理员身份运行此脚本")
    print()
    
    # 首先尝试修补当前版本
    if test_patched_wxauto():
        print("\n🎉 修补成功！可以继续使用")
    else:
        print("\n修补失败，尝试其他版本...")
        working_version = try_alternative_wxauto_versions()
        
        if working_version:
            print(f"\n🎉 找到工作版本: {working_version}")
        else:
            print("\n❌ 所有尝试都失败了")
            print("\n可能的解决方案:")
            print("1. 更新微信PC版到最新版本")
            print("2. 降级微信PC版到较老版本")
            print("3. 考虑使用其他微信自动化库")
            print("4. 检查系统兼容性设置")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
