@echo off
chcp 65001 >nul
title WChat微信客服机器人 - 安装程序
echo ========================================
echo     WChat微信客服机器人 - 安装程序
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境！
    echo.
    echo 💡 请先安装Python 3.8或更高版本：
    echo    1. 访问 https://www.python.org/downloads/
    echo    2. 下载并安装Python
    echo    3. 安装时勾选 "Add Python to PATH"
    echo    4. 重新运行此安装程序
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 安装依赖包...
pip install -r requirements.txt --no-cache-dir

if errorlevel 1 (
    echo ❌ 依赖包安装失败！
    echo 💡 尝试使用国内镜像源...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --no-cache-dir
    if errorlevel 1 (
        echo ❌ 安装仍然失败！
        echo 请检查网络连接或手动安装依赖包
        pause
        exit /b 1
    )
)

echo ✅ 依赖包安装完成

echo.
echo 🎉 WChat安装完成！
echo.
echo 📋 使用说明：
echo    1. 启动微信PC版3.9.12并登录
echo    2. 双击"启动WChat.bat"启动机器人
echo    3. 或运行"python web_config.py"打开配置界面
echo.
echo 💡 重要提示：
echo    - 确保使用微信PC版3.9.12（推荐版本）
echo    - 微信4.0.6+版本可能存在兼容性问题
echo    - 首次使用请先配置API密钥和监听列表
echo.
pause
