#!/usr/bin/env python3
"""
简单的微信连接修复工具 - Windows兼容版
"""
import os
import sys
import time
import subprocess
import psutil
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def kill_wechat_processes():
    """关闭所有微信进程"""
    print("[修复] 关闭微信进程...")
    
    killed_count = 0
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if proc.info['name'] and 'wechat' in proc.info['name'].lower():
                proc.terminate()
                killed_count += 1
                print(f"   关闭进程: {proc.info['name']} (PID: {proc.info['pid']})")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if killed_count > 0:
        print(f"[成功] 已关闭 {killed_count} 个微信进程")
        time.sleep(3)  # 等待进程完全关闭
    else:
        print("[信息] 没有需要关闭的微信进程")

def test_wxauto_connection():
    """测试wxauto连接"""
    print("[测试] 测试wxauto连接...")
    
    try:
        from wxauto import WeChat
        
        print("正在连接微信...")
        wx = WeChat()
        
        # 等待连接稳定
        time.sleep(3)
        
        # 检查连接状态
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"[成功] 微信连接成功，用户: {wx.nickname}")
            return True
        else:
            print("[错误] 微信连接失败，无法获取用户信息")
            return False
            
    except Exception as e:
        print(f"[错误] wxauto连接失败: {e}")
        return False

def main():
    """主修复流程"""
    print("=" * 60)
    print("          微信连接修复工具")
    print("=" * 60)
    
    print("此工具将帮助解决微信连接问题")
    print()
    
    # 步骤1: 关闭微信进程
    kill_wechat_processes()
    
    # 步骤2: 等待用户启动微信
    print("\n[等待] 请启动微信PC版并登录...")
    print("   1. 从开始菜单启动微信PC版")
    print("   2. 使用手机微信扫码登录")
    print("   3. 确保微信完全登录（看到聊天界面）")
    print("   4. 登录完成后按回车键继续...")
    input()
    
    # 步骤3: 测试连接
    print("\n[测试] 测试微信连接...")
    if test_wxauto_connection():
        print("\n[成功] 微信连接修复成功！")
        print("\n接下来可以:")
        print("1. 重新启动WChat机器人")
        print("2. 运行: python run.py")
    else:
        print("\n[失败] 连接仍有问题，请尝试:")
        print("1. 确保微信PC版已完全登录")
        print("2. 重启电脑后重新尝试")
        print("3. 更新wxauto: pip install --upgrade wxauto")
    
    print("\n" + "=" * 60)
    print("修复完成！")
    print("=" * 60)
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
