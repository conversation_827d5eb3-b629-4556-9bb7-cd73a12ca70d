# WChat 微信智能客服机器人

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动程序
```bash
# Windows用户
启动WChat.bat

# 或者直接运行
python run.py
```

### 3. Web配置界面
```bash
python web_config.py
```
访问: http://127.0.0.1:5000
默认密码: admin123

## 📋 功能特点

- ✅ 智能FAQ问答
- ✅ 产品信息推荐  
- ✅ AI辅助回复
- ✅ Web配置界面
- ✅ 消息监听和自动回复

## ⚙️ 配置说明

1. **微信设置**: 确保微信PC版3.9.12已登录
2. **AI配置**: 在Web界面配置API密钥
3. **数据管理**: 通过Web界面管理FAQ和产品数据

## 📞 支持

如有问题，请检查:
1. 微信PC版是否正常登录
2. Python版本是否为3.8+
3. 依赖包是否正确安装
