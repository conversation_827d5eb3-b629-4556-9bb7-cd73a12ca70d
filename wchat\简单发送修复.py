#!/usr/bin/env python3
"""
简单的发送消息修复
解决识别错误控件的问题
"""
import time
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def patch_wxauto_send_message():
    """修复wxauto的SendMsg方法"""
    try:
        import wxauto
        from wxauto import wxauto as wxauto_module
        
        # 保存原始的SendMsg方法
        original_send_msg = wxauto_module.WeChat.SendMsg
        
        def patched_send_msg(self, msg, who=None):
            """修复后的SendMsg方法"""
            try:
                # 方法1: 尝试原始方法
                return original_send_msg(self, msg, who)
            except Exception as e:
                error_str = str(e)
                print(f"原始SendMsg失败: {e}")
                
                # 如果是控件识别错误，尝试其他方法
                if "Find Control Timeout" in error_str or "搜索" in error_str:
                    print("检测到控件识别错误，尝试替代方法...")
                    
                    # 方法2: 尝试不同的参数组合
                    try:
                        if who:
                            # 先切换聊天
                            self.ChatWith(who)
                            time.sleep(0.5)
                        
                        # 尝试直接发送
                        return original_send_msg(self, msg)
                    except Exception as e2:
                        print(f"方法2失败: {e2}")
                        
                        # 方法3: 使用键盘模拟
                        try:
                            return send_by_keyboard(self, msg)
                        except Exception as e3:
                            print(f"方法3失败: {e3}")
                            raise e  # 抛出原始错误
                else:
                    raise e  # 其他错误直接抛出
        
        # 替换方法
        wxauto_module.WeChat.SendMsg = patched_send_msg
        print("✅ wxauto SendMsg补丁应用成功")
        return True
        
    except Exception as e:
        print(f"❌ 应用SendMsg补丁失败: {e}")
        return False

def send_by_keyboard(wx_instance, message):
    """使用键盘模拟发送消息"""
    try:
        # 尝试安装pyautogui
        try:
            import pyautogui
            import pyperclip
        except ImportError:
            print("安装pyautogui...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyautogui", "pyperclip"])
            import pyautogui
            import pyperclip
        
        # 获取微信窗口
        import win32gui
        hwnd = getattr(wx_instance, 'HWND', None)
        if not hwnd:
            return False
        
        # 激活微信窗口
        win32gui.SetForegroundWindow(hwnd)
        time.sleep(0.5)
        
        # 使用剪贴板发送消息
        pyperclip.copy(message)
        
        # 模拟Ctrl+V粘贴
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.3)
        
        # 按回车发送
        pyautogui.press('enter')
        time.sleep(0.3)
        
        print(f"键盘模拟发送成功: {message[:30]}...")
        return True
        
    except Exception as e:
        print(f"键盘模拟发送失败: {e}")
        return False

def test_patched_send():
    """测试修复后的发送功能"""
    print("=" * 50)
    print("测试修复后的发送功能")
    print("=" * 50)
    
    try:
        # 应用补丁
        if not patch_wxauto_send_message():
            return False
        
        import wxauto
        
        print("连接微信...")
        wx = wxauto.WeChat()
        time.sleep(3)
        
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"✅ 微信连接成功，用户: {wx.nickname}")
            
            # 测试发送消息
            test_message = "这是修复后的发送测试消息"
            print(f"测试发送: {test_message}")
            
            try:
                wx.SendMsg(msg=test_message)
                print("✅ 发送成功")
                return True
            except Exception as e:
                print(f"❌ 发送失败: {e}")
                return False
        else:
            print("❌ 微信连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def update_wechat_handler():
    """更新微信处理器使用修复后的发送方法"""
    print("\n更新微信处理器...")
    
    handler_file = Path("src/bot/wechat_handler.py")
    if not handler_file.exists():
        print("❌ 找不到wechat_handler.py文件")
        return False
    
    # 读取当前内容
    with open(handler_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经导入了补丁
    if "patch_wxauto_send_message" in content:
        print("✅ 微信处理器已包含发送补丁")
        return True
    
    # 在初始化方法中添加补丁调用
    old_init = "def initialize_wechat(self, max_retries: int = 5) -> bool:"
    new_init = """def initialize_wechat(self, max_retries: int = 5) -> bool:
        # 应用发送消息补丁
        try:
            from 简单发送修复 import patch_wxauto_send_message
            patch_wxauto_send_message()
        except Exception as e:
            logger.debug(f"应用发送补丁失败: {e}")"""
    
    if old_init in content:
        content = content.replace(old_init, new_init)
        
        # 写入修复后的内容
        with open(handler_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 微信处理器已更新")
        return True
    else:
        print("⚠️  未找到预期的初始化方法")
        return False

def main():
    """主修复流程"""
    print("=" * 60)
    print("简单发送消息修复工具")
    print("=" * 60)
    
    print("正在修复发送消息识别错误控件的问题...")
    print()
    
    # 1. 测试修复后的发送功能
    print("1. 测试修复后的发送功能...")
    if test_patched_send():
        print("✅ 发送功能修复成功")
        
        # 2. 更新微信处理器
        print("\n2. 更新微信处理器...")
        update_wechat_handler()
        
        print("\n🎉 修复完成！")
        print("\n现在可以重新启动WChat机器人:")
        print("python run.py")
        
        print("\n💡 修复说明:")
        print("- 添加了发送消息的多重保障机制")
        print("- 当原始方法失败时自动尝试替代方法")
        print("- 包含键盘模拟作为最后的备用方案")
        
    else:
        print("❌ 发送功能修复失败")
        print("\n建议:")
        print("1. 确保微信PC版正常运行")
        print("2. 确保有活跃的聊天窗口")
        print("3. 尝试重启微信PC版")
    
    print("\n" + "=" * 60)
    print("修复完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
