# Web配置界面更新说明

## 🎯 更新概述

Web配置界面已经完全更新，现在支持增强的产品管理功能和数据管理功能。

## ✅ 主要更新内容

### 1. 🧠 增强回复引擎集成
- **自动检测**：Web应用会自动检测并使用增强回复引擎
- **向下兼容**：如果增强引擎不可用，自动回退到标准引擎
- **状态显示**：仪表板显示当前使用的引擎模式

### 2. 📊 增强统计信息
- **产品数量**：显示产品数据库中的产品总数
- **产品分类**：显示所有产品分类列表
- **FAQ分类**：显示FAQ问题分类
- **增强模式标识**：显示是否使用增强功能

### 3. 🗂️ 数据管理界面
- **文件上传**：支持拖拽上传Excel/CSV文件
- **数据替换**：可选择替换或合并现有数据
- **模板下载**：提供标准的数据模板文件
- **实时反馈**：上传进度和结果提示

### 4. 🔧 新增API端点

#### `/api/stats` - 增强统计信息
```json
{
  "faq_count": 6,
  "product_count": 6,
  "product_categories": ["数码产品", "音频设备", "智能穿戴"],
  "faq_categories": ["售后服务", "物流配送", "优惠活动"],
  "enhanced_mode": true,
  "ai_available": true,
  "status": "normal"
}
```

#### `/api/reload_data` - 重新加载数据
```javascript
// POST请求，重新加载FAQ和产品数据
fetch('/api/reload_data', {method: 'POST'})
```

#### `/api/test_reply` - 测试回复功能
```javascript
// POST请求，测试消息回复
fetch('/api/test_reply', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({message: '测试消息'})
})
```

#### `/api/upload_faq` - 上传FAQ数据
```javascript
// POST请求，上传FAQ文件
const formData = new FormData();
formData.append('file', file);
formData.append('replace', 'true');
fetch('/api/upload_faq', {method: 'POST', body: formData})
```

#### `/api/upload_products` - 上传产品数据
```javascript
// POST请求，上传产品文件
const formData = new FormData();
formData.append('file', file);
formData.append('replace', 'false');
fetch('/api/upload_products', {method: 'POST', body: formData})
```

#### `/api/download_template/<type>` - 下载模板
```
GET /api/download_template/faq      # 下载FAQ模板
GET /api/download_template/products # 下载产品模板
```

### 5. 🎨 界面优化

#### 导航栏更新
- 添加了"数据管理"菜单项
- 使用Font Awesome图标
- 响应式设计

#### 仪表板增强
- **快速操作区域**：
  - 重新加载数据
  - 测试AI连接
  - 测试回复功能
  - 数据管理入口
  - FAQ管理
  - 产品管理

#### 数据管理页面
- **拖拽上传**：支持文件拖拽上传
- **数据预览**：显示数据结构说明
- **操作反馈**：Toast通知和进度提示
- **模板下载**：一键下载标准模板

## 🔄 自动适配机制

### 引擎检测
```python
# 自动检测增强引擎
try:
    from src.bot.enhanced_reply_engine import EnhancedReplyEngine as ReplyEngine
    ENHANCED_MODE = True
except ImportError:
    from src.bot.reply_engine import ReplyEngine
    ENHANCED_MODE = False
```

### 功能适配
- **增强模式**：显示产品分类、关键词匹配等高级功能
- **标准模式**：显示基础FAQ和简单产品信息
- **平滑切换**：无需修改配置即可在两种模式间切换

## 📱 页面结构

### 主要页面
1. **仪表板** (`/dashboard`) - 系统概览和快速操作
2. **基础配置** (`/config`) - 微信和AI配置
3. **FAQ管理** (`/faq`) - FAQ数据管理
4. **产品管理** (`/products`) - 产品数据管理
5. **数据管理** (`/data_management`) - 文件上传和模板下载

### 数据管理页面功能
- **FAQ数据上传**：
  - 拖拽上传区域
  - 文件格式验证
  - 替换/合并选项
  - 实时上传进度

- **产品数据上传**：
  - 支持Excel/CSV格式
  - 自动补充缺失字段
  - 数据格式验证
  - 错误提示

- **模板下载**：
  - FAQ模板：包含标准字段和示例数据
  - 产品模板：包含完整产品信息结构

## 🧪 测试功能

### 回复测试
- **测试入口**：仪表板快速操作区
- **测试流程**：输入消息 → 生成回复 → 显示结果
- **结果显示**：
  - 输入消息
  - 生成回复
  - 引擎模式（增强/标准）

### 数据重载
- **重载按钮**：仪表板快速操作
- **重载范围**：FAQ数据 + 产品数据
- **状态反馈**：成功/失败提示

## 🔧 故障排除

### 常见问题

**Q: Web界面无法启动？**
A: 检查Flask是否安装：`pip install flask`

**Q: 增强功能不显示？**
A: 确保增强数据文件存在：
- `wchat/data/faq_enhanced.xlsx`
- `wchat/data/products_enhanced.xlsx`

**Q: 文件上传失败？**
A: 检查文件格式和数据结构是否符合模板要求

**Q: 数据不更新？**
A: 上传数据后点击"重新加载数据"按钮

### 环境要求
```bash
# 必需的Python包
pip install flask pandas openpyxl werkzeug
pip install jieba fuzzywuzzy
```

### 文件权限
- 确保 `wchat/data/` 目录可写
- 确保 `wchat/temp/` 目录存在且可写

## 🚀 启动方法

### 方法1：使用启动器（推荐）
```bash
python wchat\启动器.py
# 选择 [2] 启动Web配置界面
```

### 方法2：直接启动
```bash
python wchat\src\web\app.py
```

### 方法3：使用Web配置脚本
```bash
python wchat\web_config.py
```

## 📊 功能对比

| 功能 | 标准模式 | 增强模式 |
|------|----------|----------|
| FAQ管理 | ✅ 基础 | ✅ 关键词匹配 |
| 产品管理 | ✅ 简单 | ✅ 智能搜索 |
| 数据上传 | ❌ | ✅ 完整支持 |
| 模板下载 | ❌ | ✅ 标准模板 |
| 分类管理 | ❌ | ✅ 自动分类 |
| 关键词匹配 | ❌ | ✅ 模糊匹配 |
| 产品图片 | ❌ | ✅ 图片支持 |
| 详细信息 | ❌ | ✅ 规格参数 |

## 🎯 使用建议

### 数据准备
1. **下载模板**：先下载标准模板了解数据格式
2. **准备数据**：按照模板格式整理FAQ和产品数据
3. **测试上传**：先上传少量数据测试功能
4. **批量导入**：确认无误后批量上传完整数据

### 最佳实践
1. **定期备份**：重要数据更新前先备份
2. **分批上传**：大量数据分批上传避免超时
3. **测试回复**：数据更新后测试回复效果
4. **监控日志**：关注Web应用日志了解运行状态

## 📞 技术支持

如遇问题，请：
1. 检查浏览器控制台错误信息
2. 查看Web应用日志输出
3. 确认数据文件格式正确
4. 验证网络连接和端口访问

---

**🎉 Web配置界面现已完全支持增强的产品管理功能！**

访问地址：`http://localhost:5000`
默认密码：`admin123`
