
class FallbackWeChatHandler:
    """备用微信处理器 - 仅Web界面模式"""
    
    def __init__(self):
        self.wx = None
        self.running = False
        
    def initialize_wechat(self, max_retries=3):
        """初始化微信连接 - 备用模式"""
        print("备用模式：跳过微信连接，仅启用Web界面")
        return False
        
    def start_listening(self):
        """启动监听 - 备用模式"""
        print("备用模式：微信监听功能已禁用")
        self.running = False
        
    def stop_listening(self):
        """停止监听"""
        self.running = False
        
    def get_status(self):
        """获取状态"""
        return {
            "connected": False,
            "listening": False,
            "mode": "fallback",
            "message": "微信连接失败，仅Web界面可用"
        }
