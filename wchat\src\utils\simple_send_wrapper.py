
def safe_send_msg(wx_instance, message, chat_name=None):
    """安全发送消息的包装器"""
    import time
    
    try:
        # 如果指定了聊天对象，先切换
        if chat_name:
            try:
                wx_instance.ChatWith(chat_name)
                time.sleep(0.5)
            except Exception as e:
                print(f"切换聊天失败: {e}")
        
        # 尝试发送消息
        try:
            wx_instance.SendMsg(msg=message)
            print(f"发送成功: {message[:30]}...")
            return True
        except Exception as e:
            print(f"SendMsg失败: {e}")
            
            # 尝试键盘模拟
            try:
                import pyautogui
                import pyperclip
                
                pyperclip.copy(message)
                time.sleep(0.3)
                pyautogui.hotkey('ctrl', 'v')
                time.sleep(0.3)
                pyautogui.press('enter')
                time.sleep(0.3)
                
                print(f"键盘模拟发送成功: {message[:30]}...")
                return True
            except Exception as e2:
                print(f"键盘模拟也失败: {e2}")
                return False
                
    except Exception as e:
        print(f"发送消息异常: {e}")
        return False
