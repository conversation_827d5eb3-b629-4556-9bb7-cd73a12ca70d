# wchat微信智能客服机器人

## 🎯 项目简介

wchat是一个功能完整的微信智能客服机器人，支持：

- 🎤 **语音消息识别** - 自动将语音转换为文字
- 💬 **智能对话回复** - FAQ自动回复 + 产品推荐 + AI对话
- 🛍️ **产品推荐系统** - 关键词匹配 + 图片展示
- 📚 **FAQ管理** - Excel数据库，易于维护
- 🌐 **Web管理界面** - 可视化配置和数据管理
- 🔄 **自动重连** - 微信连接稳定性保障

## 🚀 快速开始

### 1. 系统要求

- **操作系统**: Windows 10/11 (推荐)
- **Python版本**: 3.8 或更高版本
- **微信**: 微信PC版 (必须)
- **网络**: 需要网络连接 (AI功能)

### 2. 安装步骤

#### Windows用户
1. 双击运行 `安装.bat`
2. 等待依赖包安装完成
3. 编辑配置文件 `wchat/config/config.json`
4. 双击运行 `启动.bat`

#### Linux/Mac用户
1. 运行 `chmod +x install.sh && ./install.sh`
2. 等待依赖包安装完成
3. 编辑配置文件 `wchat/config/config.json`
4. 运行 `./start.sh`

### 3. 配置说明

编辑 `wchat/config/config.json` 文件：

```json
{
  "ai": {
    "enabled": true,
    "api_url": "https://api.siliconflow.cn/v1/chat/completions",
    "api_key": "你的API密钥",
    "model": "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B"
  },
  "wechat": {
    "voice_to_text": true,
    "global_listen": true,
    "reply_delay": 1
  }
}
```

**重要配置项**:
- `ai.api_key`: 设置你的AI API密钥
- `wechat.global_listen`: true=监听所有消息，false=仅监听指定联系人
- `wechat.voice_to_text`: 是否启用语音识别

### 4. 数据管理

- **FAQ数据**: `wchat/data/faq.xlsx`
- **产品数据**: `wchat/data/products.xlsx`
- **产品图片**: `wchat/static/images/`

### 5. Web管理界面

启动后访问: http://localhost:5000
- 默认密码: admin123
- 可以在线管理FAQ和产品数据

## 📋 功能说明

### 语音识别
- 自动识别微信语音消息
- 转换为文字后进行智能回复
- 无需额外配置，开箱即用

### 智能回复
1. **FAQ优先**: 匹配FAQ数据库
2. **产品推荐**: 关键词匹配产品
3. **AI对话**: 兜底的AI智能回复

### 产品推荐
- 支持关键词匹配
- 自动发送产品图片
- 可配置推荐话术

## 🔧 故障排除

### 常见问题

1. **微信连接失败**
   - 确保微信PC版已登录
   - 以管理员身份运行程序
   - 检查微信版本兼容性

2. **语音识别不工作**
   - 检查微信版本
   - 确认wxauto库版本兼容

3. **AI回复异常**
   - 检查API密钥是否正确
   - 验证网络连接
   - 确认API服务可用

4. **图片发送失败**
   - 检查图片文件是否存在
   - 确认图片路径正确
   - 验证文件权限

### 日志查看

程序运行时会显示详细日志，包括：
- 微信连接状态
- 消息处理过程
- 错误信息和解决建议

## 📞 技术支持

如果遇到问题，请：
1. 查看程序运行日志
2. 检查配置文件是否正确
3. 确认系统环境符合要求
4. 尝试重启程序

## 📄 许可证

本项目仅供学习和研究使用。

---

**wchat微信智能客服机器人 - 让客服更智能，让沟通更高效！** 🎤🤖✨
