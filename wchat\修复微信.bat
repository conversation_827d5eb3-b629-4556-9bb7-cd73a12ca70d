@echo off
title 微信连接修复工具
chcp 65001 >nul

echo ========================================
echo     微信连接修复工具
echo ========================================
echo.

echo [检查] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Python环境！
    echo [提示] 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo [成功] Python环境检查通过

echo.
echo [启动] 运行修复程序...
cd wchat
python 简单修复.py

echo.
echo [完成] 修复程序已退出
pause
