wchat配置说明
================

📋 主要配置文件: wchat/config/config.json

🔑 必须配置的项目:
1. ai.api_key - AI API密钥 (必须设置)
2. wechat.global_listen - 监听模式选择

🌐 推荐的AI服务商:
- 硅基流动 (SiliconFlow): https://siliconflow.cn/
- DeepSeek: https://platform.deepseek.com/
- 智谱AI: https://open.bigmodel.cn/

📊 数据文件位置:
- FAQ数据: wchat/data/faq.xlsx
- 产品数据: wchat/data/products.xlsx
- 产品图片: wchat/static/images/

🌐 Web管理界面:
- 地址: http://localhost:5000
- 密码: admin123

💡 使用提示:
1. 首次使用请先配置AI API密钥
2. 可以通过Web界面管理数据
3. 支持热更新，无需重启程序
4. 建议以管理员身份运行

🔧 故障排除:
1. 微信连接问题 - 检查微信PC版状态
2. 语音识别问题 - 确认wxauto版本兼容
3. AI回复问题 - 验证API密钥和网络
4. 图片发送问题 - 检查文件路径和权限
