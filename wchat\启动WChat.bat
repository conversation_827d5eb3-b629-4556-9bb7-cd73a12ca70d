@echo off
title WChat System

echo.
echo ============================================================
echo                    WChat System Launcher
echo ============================================================
echo.

cd /d "%~dp0"

echo Select startup mode:
echo [1] Web interface only
echo [2] WeChat bot only
echo [3] Both (Recommended)
echo [4] Options menu
echo [0] Exit
echo.

set /p choice=Enter choice (0-4):

if "%choice%"=="0" goto exit
if "%choice%"=="1" goto web_only
if "%choice%"=="2" goto bot_only
if "%choice%"=="3" goto both
if "%choice%"=="4" goto options
echo Invalid choice, starting full system...
goto both

:web_only
echo.
echo Starting web interface...
python start_web_only.py
goto end

:bot_only
echo.
echo Starting WeChat bot...
echo Important notes:
echo 1. Make sure WeChat PC is logged in
echo 2. Configure listen list in web interface first
echo.
echo Running quick diagnosis...
python 快速诊断.py
echo.
echo Starting safe mode...
python 安全启动.py
goto end

:both
echo.
echo Starting full system...
python quick_start.py
goto end

:options
echo.
echo Options menu...
python start_with_options.py
goto end

:end
echo.
echo Press any key to exit...
pause >nul
goto exit

:exit
echo.
echo Thank you for using WChat System!
exit
