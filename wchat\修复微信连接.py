#!/usr/bin/env python3
"""
微信连接问题修复工具
专门解决 "无效的窗口句柄" 错误
"""
import os
import sys
import time
import subprocess
import psutil
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_wechat_process():
    """检查微信进程状态"""
    print("🔍 检查微信进程...")
    
    wechat_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            if proc.info['name'] and 'wechat' in proc.info['name'].lower():
                wechat_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if wechat_processes:
        print(f"✅ 找到 {len(wechat_processes)} 个微信进程:")
        for proc in wechat_processes:
            print(f"   PID: {proc['pid']}, 名称: {proc['name']}")
        return True
    else:
        print("❌ 未找到微信进程")
        return False

def kill_wechat_processes():
    """关闭所有微信进程"""
    print("🔄 关闭微信进程...")
    
    killed_count = 0
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if proc.info['name'] and 'wechat' in proc.info['name'].lower():
                proc.terminate()
                killed_count += 1
                print(f"   关闭进程: {proc.info['name']} (PID: {proc.info['pid']})")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if killed_count > 0:
        print(f"✅ 已关闭 {killed_count} 个微信进程")
        time.sleep(3)  # 等待进程完全关闭
    else:
        print("ℹ️  没有需要关闭的微信进程")

def find_wechat_executable():
    """查找微信可执行文件"""
    print("🔍 查找微信安装路径...")
    
    # 常见的微信安装路径
    common_paths = [
        r"C:\Program Files\Tencent\WeChat\WeChat.exe",
        r"C:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
        r"D:\Program Files\Tencent\WeChat\WeChat.exe",
        r"D:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
    ]
    
    # 检查常见路径
    for path in common_paths:
        if os.path.exists(path):
            print(f"✅ 找到微信: {path}")
            return path
    
    # 尝试从注册表查找
    try:
        import winreg
        key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                           r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall")
        
        for i in range(winreg.QueryInfoKey(key)[0]):
            try:
                subkey_name = winreg.EnumKey(key, i)
                subkey = winreg.OpenKey(key, subkey_name)
                
                try:
                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                    if "微信" in display_name or "WeChat" in display_name:
                        install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                        wechat_exe = os.path.join(install_location, "WeChat.exe")
                        if os.path.exists(wechat_exe):
                            print(f"✅ 从注册表找到微信: {wechat_exe}")
                            return wechat_exe
                except FileNotFoundError:
                    pass
                finally:
                    winreg.CloseKey(subkey)
            except OSError:
                continue
        winreg.CloseKey(key)
    except ImportError:
        pass
    
    print("❌ 未找到微信安装路径")
    return None

def start_wechat():
    """启动微信"""
    print("🚀 启动微信...")
    
    wechat_path = find_wechat_executable()
    if not wechat_path:
        print("❌ 无法找到微信可执行文件")
        print("请手动启动微信PC版并登录")
        return False
    
    try:
        subprocess.Popen([wechat_path])
        print("✅ 微信启动命令已执行")
        print("⏳ 等待微信启动...")
        time.sleep(5)
        
        # 检查是否启动成功
        if check_wechat_process():
            print("✅ 微信启动成功")
            return True
        else:
            print("❌ 微信启动失败")
            return False
    except Exception as e:
        print(f"❌ 启动微信失败: {e}")
        return False

def wait_for_login():
    """等待用户登录"""
    print("\n📱 请在微信PC端完成登录...")
    print("   1. 使用手机微信扫码登录")
    print("   2. 确保微信保持在前台或最小化状态")
    print("   3. 登录完成后按回车键继续...")
    input()

def test_wxauto_connection():
    """测试wxauto连接"""
    print("🔍 测试wxauto连接...")
    
    try:
        from wxauto import WeChat
        
        print("正在连接微信...")
        wx = WeChat()
        
        # 等待连接稳定
        time.sleep(2)
        
        # 检查连接状态
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"✅ 微信连接成功，用户: {wx.nickname}")
            return True
        else:
            print("❌ 微信连接失败，无法获取用户信息")
            return False
            
    except Exception as e:
        print(f"❌ wxauto连接失败: {e}")
        return False

def fix_wechat_handler():
    """修复微信处理器初始化问题"""
    print("🔧 修复微信处理器...")
    
    # 修改wechat_handler.py中的初始化逻辑
    handler_file = current_dir / "src" / "bot" / "wechat_handler.py"
    
    if not handler_file.exists():
        print("❌ 找不到wechat_handler.py文件")
        return False
    
    # 创建备份
    backup_file = handler_file.with_suffix('.py.backup')
    if not backup_file.exists():
        import shutil
        shutil.copy2(handler_file, backup_file)
        print(f"✅ 已创建备份: {backup_file}")
    
    # 读取当前内容
    with open(handler_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否需要修复
    if 'time.sleep(5)' in content and 'max_retries: int = 5' in content:
        print("✅ 微信处理器已经是修复版本")
        return True
    
    # 应用修复
    # 增加重试次数和等待时间
    content = content.replace(
        'def initialize_wechat(self, max_retries: int = 3) -> bool:',
        'def initialize_wechat(self, max_retries: int = 5) -> bool:'
    )
    
    content = content.replace(
        'time.sleep(2.0)  # 增加到2秒',
        'time.sleep(5.0)  # 增加到5秒，确保微信完全初始化'
    )
    
    content = content.replace(
        'time.sleep(3)',
        'time.sleep(5)'
    )
    
    # 写入修复后的内容
    with open(handler_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 微信处理器修复完成")
    return True

def main():
    """主修复流程"""
    print("=" * 60)
    print("          微信连接问题修复工具")
    print("=" * 60)
    
    print("此工具将帮助解决 '无效的窗口句柄' 错误")
    print()
    
    # 步骤1: 检查当前微信状态
    if check_wechat_process():
        print("\n🔄 建议重启微信以确保连接稳定")
        choice = input("是否重启微信? (y/n): ").lower()
        if choice == 'y':
            kill_wechat_processes()
            if not start_wechat():
                print("请手动启动微信")
            wait_for_login()
    else:
        # 步骤2: 启动微信
        if not start_wechat():
            print("请手动启动微信PC版")
        wait_for_login()
    
    # 步骤3: 修复处理器
    fix_wechat_handler()
    
    # 步骤4: 测试连接
    print("\n🧪 测试微信连接...")
    if test_wxauto_connection():
        print("\n🎉 微信连接修复成功！")
        print("\n接下来可以:")
        print("1. 重新启动WChat机器人")
        print("2. 运行: python run.py")
    else:
        print("\n❌ 连接仍有问题，请尝试:")
        print("1. 确保微信PC版已完全登录")
        print("2. 重启电脑后重新尝试")
        print("3. 更新wxauto: pip install --upgrade wxauto")
        print("4. 检查微信版本是否过新（可能不兼容）")
    
    print("\n" + "=" * 60)
    print("修复完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
