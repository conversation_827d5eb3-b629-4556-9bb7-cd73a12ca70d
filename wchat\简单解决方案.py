#!/usr/bin/env python3
"""
简单的wxauto问题解决方案
"""
import subprocess
import sys
import time

def install_working_version():
    """安装一个已知工作的wxauto版本"""
    print("正在安装兼容版本的wxauto...")
    
    try:
        # 卸载当前版本
        print("卸载当前版本...")
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "wxauto", "-y"], 
                      check=True, capture_output=True)
        
        # 安装较老的稳定版本
        print("安装兼容版本...")
        subprocess.run([sys.executable, "-m", "pip", "install", "wxauto==********.3"], 
                      check=True, capture_output=True)
        
        print("✅ 安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False

def test_simple_connection():
    """简单测试连接"""
    print("\n测试微信连接...")
    
    try:
        # 重新导入wxauto
        if 'wxauto' in sys.modules:
            del sys.modules['wxauto']
        
        import wxauto
        
        print("创建微信对象...")
        wx = wxauto.WeChat()
        
        print("等待连接...")
        time.sleep(5)
        
        # 简单测试
        if hasattr(wx, 'nickname'):
            nickname = wx.nickname
            if nickname:
                print(f"✅ 连接成功！用户: {nickname}")
                return True
        
        print("⚠️  连接部分成功，但功能可能受限")
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def create_fallback_handler():
    """创建备用的微信处理器"""
    print("\n创建备用处理器...")
    
    fallback_code = '''
class FallbackWeChatHandler:
    """备用微信处理器 - 仅Web界面模式"""
    
    def __init__(self):
        self.wx = None
        self.running = False
        
    def initialize_wechat(self, max_retries=3):
        """初始化微信连接 - 备用模式"""
        print("备用模式：跳过微信连接，仅启用Web界面")
        return False
        
    def start_listening(self):
        """启动监听 - 备用模式"""
        print("备用模式：微信监听功能已禁用")
        self.running = False
        
    def stop_listening(self):
        """停止监听"""
        self.running = False
        
    def get_status(self):
        """获取状态"""
        return {
            "connected": False,
            "listening": False,
            "mode": "fallback",
            "message": "微信连接失败，仅Web界面可用"
        }
'''
    
    # 写入备用处理器
    with open('fallback_handler.py', 'w', encoding='utf-8') as f:
        f.write(fallback_code)
    
    print("✅ 备用处理器已创建")

def main():
    """主函数"""
    print("=" * 50)
    print("wxauto问题简单解决方案")
    print("=" * 50)
    
    print("检测到wxauto与微信版本不兼容")
    print("尝试以下解决方案:\n")
    
    # 方案1：安装兼容版本
    print("方案1：安装兼容版本的wxauto")
    if install_working_version():
        if test_simple_connection():
            print("\n🎉 问题解决！现在可以启动WChat了")
            print("运行: python run.py")
            input("\n按回车键退出...")
            return
    
    # 方案2：创建备用处理器
    print("\n方案2：创建备用模式")
    create_fallback_handler()
    
    print("\n📋 备用方案说明:")
    print("1. 微信机器人功能暂时不可用")
    print("2. 可以使用Web配置界面")
    print("3. 可以管理FAQ和产品数据")
    print("4. 等待wxauto兼容性问题解决")
    
    print("\n🔧 启动Web界面:")
    print("运行: python web_config.py")
    
    print("\n💡 完整解决方案:")
    print("1. 更新微信PC版到最新版本")
    print("2. 或降级到wxauto兼容的微信版本")
    print("3. 等待wxauto库更新")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
