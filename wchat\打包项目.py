#!/usr/bin/env python3
"""
WChat项目完整打包工具
创建可在其他电脑运行的完整包
"""
import os
import shutil
import zipfile
import json
from pathlib import Path
from datetime import datetime

def create_package_structure():
    """创建打包目录结构"""
    package_name = f"WChat_v1.0_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    package_dir = Path(f"../WChat_Package/{package_name}")
    
    # 创建打包目录
    package_dir.mkdir(parents=True, exist_ok=True)
    
    return package_dir, package_name

def copy_core_files(package_dir):
    """复制核心文件"""
    print("复制核心文件...")
    
    # 核心文件列表
    core_files = [
        'run.py',
        'web_config.py', 
        'quick_start.py',
        'requirements.txt',
        'README.md',
        '启动WChat.bat'
    ]
    
    # 复制文件
    for file_name in core_files:
        if Path(file_name).exists():
            shutil.copy2(file_name, package_dir / file_name)
            print(f"  ✅ {file_name}")
        else:
            print(f"  ⚠️  {file_name} 不存在")
    
    # 复制核心目录
    core_dirs = ['src', 'config', 'data']
    for dir_name in core_dirs:
        if Path(dir_name).exists():
            shutil.copytree(dir_name, package_dir / dir_name, dirs_exist_ok=True)
            print(f"  ✅ {dir_name}/ 目录")
        else:
            print(f"  ⚠️  {dir_name}/ 目录不存在")

def create_installer_script(package_dir):
    """创建安装脚本"""
    print("创建安装脚本...")
    
    # Windows安装脚本
    install_bat = """@echo off
chcp 65001 >nul
title WChat微信客服机器人 - 安装程序
echo ========================================
echo     WChat微信客服机器人 - 安装程序
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境！
    echo.
    echo 💡 请先安装Python 3.8或更高版本：
    echo    1. 访问 https://www.python.org/downloads/
    echo    2. 下载并安装Python
    echo    3. 安装时勾选 "Add Python to PATH"
    echo    4. 重新运行此安装程序
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 安装依赖包...
pip install -r requirements.txt --no-cache-dir

if errorlevel 1 (
    echo ❌ 依赖包安装失败！
    echo 💡 尝试使用国内镜像源...
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --no-cache-dir
    if errorlevel 1 (
        echo ❌ 安装仍然失败！
        echo 请检查网络连接或手动安装依赖包
        pause
        exit /b 1
    )
)

echo ✅ 依赖包安装完成

echo.
echo 🎉 WChat安装完成！
echo.
echo 📋 使用说明：
echo    1. 启动微信PC版3.9.12并登录
echo    2. 双击"启动WChat.bat"启动机器人
echo    3. 或运行"python web_config.py"打开配置界面
echo.
echo 💡 重要提示：
echo    - 确保使用微信PC版3.9.12（推荐版本）
echo    - 微信4.0.6+版本可能存在兼容性问题
echo    - 首次使用请先配置API密钥和监听列表
echo.
pause
"""
    
    with open(package_dir / "安装.bat", 'w', encoding='utf-8') as f:
        f.write(install_bat)
    
    print("  ✅ 安装.bat")

def create_startup_scripts(package_dir):
    """创建启动脚本"""
    print("创建启动脚本...")
    
    # 改进的启动脚本
    startup_bat = """@echo off
chcp 65001 >nul
title WChat微信客服机器人
echo ========================================
echo     WChat微信客服机器人
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python环境未找到！
    echo 请先运行"安装.bat"安装依赖
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 🔍 检查依赖包...
python -c "import wxauto" >nul 2>&1
if errorlevel 1 (
    echo ❌ 依赖包未安装！
    echo 请先运行"安装.bat"安装依赖
    pause
    exit /b 1
)

echo ✅ 依赖包正常

echo.
echo 🚀 启动WChat机器人...
echo 💡 请确保微信PC版已启动并登录
echo.
python run.py

echo.
echo 程序已退出
pause
"""
    
    with open(package_dir / "启动WChat.bat", 'w', encoding='utf-8') as f:
        f.write(startup_bat)
    
    # Web配置启动脚本
    web_config_bat = """@echo off
chcp 65001 >nul
title WChat配置界面
echo ========================================
echo     WChat配置界面
echo ========================================
echo.

echo 🚀 启动Web配置界面...
echo 💡 配置完成后请访问: http://127.0.0.1:5000
echo 💡 默认密码: admin123
echo.
python web_config.py

echo.
echo 配置界面已关闭
pause
"""
    
    with open(package_dir / "配置界面.bat", 'w', encoding='utf-8') as f:
        f.write(web_config_bat)
    
    print("  ✅ 启动WChat.bat")
    print("  ✅ 配置界面.bat")

def create_documentation(package_dir):
    """创建完整文档"""
    print("创建使用文档...")
    
    readme_content = """# WChat 微信智能客服机器人

## 📋 项目简介

WChat是一个基于Python开发的微信智能客服机器人，支持：
- 🤖 智能FAQ问答
- 📦 产品信息推荐
- 🧠 AI辅助回复
- 🌐 Web配置界面
- 📊 数据管理功能

## 🚀 快速开始

### 1. 系统要求
- Windows 10/11
- Python 3.8或更高版本
- 微信PC版3.9.12（推荐）

### 2. 安装步骤
1. **解压文件包**到任意目录
2. **双击运行"安装.bat"**安装依赖包
3. **启动微信PC版并登录**
4. **双击"启动WChat.bat"**启动机器人

### 3. 首次配置
1. **双击"配置界面.bat"**打开Web配置
2. **访问**: http://127.0.0.1:5000
3. **登录密码**: admin123
4. **配置AI API密钥**（必需）
5. **添加监听列表**（可选，默认全局监听）

## ⚙️ 详细配置

### AI配置
1. 在Web界面进入"AI配置"页面
2. 选择AI服务商（推荐SiliconFlow）
3. 输入API密钥
4. 选择模型（推荐DeepSeek系列）

### 微信配置
1. 在Web界面进入"微信配置"页面
2. 设置自动回复开关
3. 配置回复延迟时间
4. 添加要监听的聊天对象（留空则全局监听）

### 数据管理
1. **FAQ管理**: 添加常见问题和答案
2. **产品管理**: 添加产品信息和图片
3. **数据导入**: 支持Excel文件批量导入

## 📁 文件结构

```
WChat/
├── 安装.bat                    # 安装脚本
├── 启动WChat.bat               # 启动机器人
├── 配置界面.bat                # Web配置界面
├── README.md                   # 使用说明
├── requirements.txt            # 依赖包列表
├── run.py                     # 主程序
├── web_config.py              # Web配置程序
├── config/                    # 配置文件目录
│   └── config.json           # 主配置文件
├── data/                     # 数据目录
│   ├── faq.xlsx             # FAQ数据
│   ├── products.xlsx        # 产品数据
│   ├── images/              # 产品图片
│   └── logs/                # 日志文件
└── src/                     # 源代码目录
    ├── ai/                  # AI服务
    ├── bot/                 # 机器人核心
    ├── database/            # 数据处理
    ├── utils/               # 工具类
    └── web/                 # Web界面
```

## 🔧 常见问题

### Q: 微信连接失败怎么办？
A: 
1. 确保使用微信PC版3.9.12
2. 确保微信已完全登录
3. 尝试重启微信和程序
4. 检查是否有安全软件阻止

### Q: AI回复不工作？
A:
1. 检查API密钥是否正确配置
2. 检查网络连接
3. 确认API服务商账户余额

### Q: 无法监听消息？
A:
1. 检查监听列表配置
2. 确认微信窗口状态
3. 查看日志文件排查问题

### Q: 如何更新FAQ和产品数据？
A:
1. 使用Web界面在线编辑
2. 直接编辑Excel文件
3. 通过API批量导入

## 📞 技术支持

### 日志文件位置
- 程序日志: `data/logs/`
- 错误信息: 查看命令行输出

### 配置文件位置
- 主配置: `config/config.json`
- 数据文件: `data/` 目录

### 常用命令
```bash
# 启动机器人
python run.py

# 启动配置界面
python web_config.py

# 检查依赖
pip list | findstr wxauto

# 重新安装依赖
pip install -r requirements.txt
```

## 📝 版本信息

- **版本**: v1.0
- **发布日期**: 2025-07-17
- **Python版本**: 3.8+
- **微信版本**: 3.9.12（推荐）

## ⚠️ 重要提示

1. **微信版本**: 强烈推荐使用微信PC版3.9.12，新版本可能存在兼容性问题
2. **API密钥**: 首次使用必须配置AI API密钥才能使用智能回复功能
3. **网络环境**: 需要稳定的网络连接访问AI服务
4. **数据备份**: 建议定期备份配置文件和数据文件

---

**祝你使用愉快！** 🎉
"""
    
    with open(package_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("  ✅ README.md")

def create_version_info(package_dir, package_name):
    """创建版本信息文件"""
    print("创建版本信息...")
    
    version_info = {
        "name": "WChat微信智能客服机器人",
        "version": "1.0",
        "build_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "package_name": package_name,
        "python_version": "3.8+",
        "wechat_version": "3.9.12 (推荐)",
        "features": [
            "智能FAQ问答",
            "产品信息推荐", 
            "AI辅助回复",
            "Web配置界面",
            "数据管理功能"
        ],
        "requirements": [
            "Windows 10/11",
            "Python 3.8+",
            "微信PC版3.9.12",
            "稳定网络连接"
        ]
    }
    
    with open(package_dir / "version.json", 'w', encoding='utf-8') as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    
    print("  ✅ version.json")

def create_zip_package(package_dir, package_name):
    """创建ZIP压缩包"""
    print("创建ZIP压缩包...")
    
    zip_path = package_dir.parent / f"{package_name}.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(package_dir.parent)
                zipf.write(file_path, arc_path)
    
    print(f"  ✅ {zip_path}")
    return zip_path

def main():
    """主打包流程"""
    print("=" * 60)
    print("WChat项目完整打包工具")
    print("=" * 60)
    
    try:
        # 1. 创建打包目录
        package_dir, package_name = create_package_structure()
        print(f"创建打包目录: {package_dir}")
        
        # 2. 复制核心文件
        copy_core_files(package_dir)
        
        # 3. 创建安装脚本
        create_installer_script(package_dir)
        
        # 4. 创建启动脚本
        create_startup_scripts(package_dir)
        
        # 5. 创建文档
        create_documentation(package_dir)
        
        # 6. 创建版本信息
        create_version_info(package_dir, package_name)
        
        # 7. 创建ZIP包
        zip_path = create_zip_package(package_dir, package_name)
        
        # 8. 显示结果
        print("\n" + "=" * 60)
        print("打包完成！")
        print("=" * 60)
        print(f"📦 打包目录: {package_dir}")
        print(f"📦 ZIP文件: {zip_path}")
        print(f"📦 文件大小: {zip_path.stat().st_size / 1024 / 1024:.1f} MB")
        
        print("\n📋 包含内容:")
        print("  ✅ 完整源代码")
        print("  ✅ 配置文件和数据")
        print("  ✅ 安装脚本")
        print("  ✅ 启动脚本")
        print("  ✅ 使用文档")
        print("  ✅ 版本信息")
        
        print("\n🚀 使用方法:")
        print("  1. 将ZIP文件发送到目标电脑")
        print("  2. 解压到任意目录")
        print("  3. 双击'安装.bat'安装依赖")
        print("  4. 双击'启动WChat.bat'运行程序")
        
        print("\n🎉 打包成功！")
        
    except Exception as e:
        print(f"\n❌ 打包失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
