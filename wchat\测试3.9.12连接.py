#!/usr/bin/env python3
"""
测试微信3.9.12版本的连接
"""
import time
import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_wxauto_direct():
    """直接测试wxauto"""
    print("=" * 50)
    print("直接测试wxauto连接")
    print("=" * 50)
    
    try:
        import wxauto
        print("正在连接微信...")
        
        wx = wxauto.WeChat()
        time.sleep(3)
        
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"✅ 连接成功！用户: {wx.nickname}")
            
            # 测试GetNewMessage
            print("测试GetNewMessage方法...")
            try:
                msgs = wx.GetNewMessage()
                print(f"✅ GetNewMessage成功，返回 {len(msgs) if msgs else 0} 条消息")
                return True
            except Exception as e:
                print(f"❌ GetNewMessage失败: {e}")
                
                # 尝试修复
                print("尝试修复内部状态...")
                try:
                    if hasattr(wx, '_msgs') and wx._msgs is None:
                        print("检测到_msgs为None，尝试重新初始化...")
                        if hasattr(wx, '_get_msgs'):
                            wx._msgs = wx._get_msgs()
                            print("重新初始化_msgs成功")
                        
                    # 再次测试
                    msgs = wx.GetNewMessage()
                    print(f"✅ 修复后GetNewMessage成功，返回 {len(msgs) if msgs else 0} 条消息")
                    return True
                except Exception as e2:
                    print(f"❌ 修复失败: {e2}")
                    return False
        else:
            print("❌ 无法获取用户信息")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_wechat_handler():
    """测试修复后的微信处理器"""
    print("\n" + "=" * 50)
    print("测试修复后的微信处理器")
    print("=" * 50)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        print("创建微信处理器...")
        handler = WeChatHandler()
        
        print("初始化微信连接...")
        if handler.initialize_wechat():
            print("✅ 微信处理器初始化成功")
            
            print("测试消息监听...")
            handler.start_listening()
            
            if handler.running:
                print("✅ 消息监听启动成功")
                
                # 运行一小段时间测试
                print("运行10秒测试...")
                time.sleep(10)
                
                handler.stop_listening()
                print("✅ 测试完成")
                return True
            else:
                print("❌ 消息监听启动失败")
                return False
        else:
            print("❌ 微信处理器初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 微信处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("微信3.9.12版本连接测试")
    print("请确保微信3.9.12已启动并登录")
    print()
    
    # 测试1: 直接测试wxauto
    wxauto_ok = test_wxauto_direct()
    
    # 测试2: 测试修复后的处理器
    handler_ok = test_wechat_handler()
    
    print("\n" + "=" * 50)
    print("测试结果")
    print("=" * 50)
    
    print(f"wxauto直接连接: {'✅ 成功' if wxauto_ok else '❌ 失败'}")
    print(f"微信处理器: {'✅ 成功' if handler_ok else '❌ 失败'}")
    
    if wxauto_ok and handler_ok:
        print("\n🎉 所有测试通过！")
        print("现在可以正常启动WChat机器人了")
        print("运行: python run.py")
    elif wxauto_ok:
        print("\n⚠️  wxauto可以连接，但处理器有问题")
        print("可能需要进一步调试处理器代码")
    else:
        print("\n❌ 连接仍有问题")
        print("可能需要:")
        print("1. 重启微信")
        print("2. 检查微信版本")
        print("3. 更新wxauto版本")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
