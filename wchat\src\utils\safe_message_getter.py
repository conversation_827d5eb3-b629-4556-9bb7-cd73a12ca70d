#!/usr/bin/env python3
"""
安全的微信消息获取工具
避免wxauto内部错误
"""
import time
from typing import List, Any, Optional

def safe_get_new_message(wx_instance) -> List[Any]:
    """
    安全的消息获取方法，避免wxauto内部错误
    
    Args:
        wx_instance: 微信实例对象
        
    Returns:
        List[Any]: 消息列表，如果获取失败返回空列表
    """
    if not wx_instance:
        return []
    
    # 方法1: 尝试GetNewMessage
    try:
        msgs = wx_instance.GetNewMessage()
        if msgs and isinstance(msgs, list):
            return msgs
    except Exception as e:
        error_str = str(e)
        if "cannot access local variable 'length'" in error_str:
            # 这是wxauto内部的变量作用域错误，继续尝试其他方法
            pass
        elif "NoneType" in error_str:
            # 连接问题，继续尝试其他方法
            pass
        else:
            # 其他未知错误，记录但继续
            print(f"GetNewMessage未知错误: {e}")
    
    # 方法2: 尝试GetNextNewMessage
    try:
        next_msg = wx_instance.GetNextNewMessage()
        if next_msg and isinstance(next_msg, dict):
            content = next_msg.get('content', '') or next_msg.get('text', '')
            if content:
                return [next_msg]
    except Exception as e:
        # GetNextNewMessage失败，继续尝试下一个方法
        pass
    
    # 方法3: 尝试GetAllMessage并取最新的
    try:
        all_msgs = wx_instance.GetAllMessage()
        if all_msgs and isinstance(all_msgs, list) and len(all_msgs) > 0:
            # 只返回最新的3条消息，避免重复处理
            recent_msgs = all_msgs[-3:] if len(all_msgs) > 3 else all_msgs
            # 过滤掉可能的重复或无效消息
            valid_msgs = []
            for msg in recent_msgs:
                if hasattr(msg, 'content') or hasattr(msg, 'text'):
                    content = getattr(msg, 'content', '') or getattr(msg, 'text', '')
                    if content and content.strip():
                        valid_msgs.append(msg)
            return valid_msgs
    except Exception as e:
        # GetAllMessage也失败了
        pass
    
    # 所有方法都失败，返回空列表
    return []

def safe_get_current_chat(wx_instance) -> Optional[str]:
    """
    安全获取当前聊天对象
    
    Args:
        wx_instance: 微信实例对象
        
    Returns:
        Optional[str]: 当前聊天对象名称，失败返回None
    """
    if not wx_instance:
        return None
    
    try:
        current_chat = wx_instance.CurrentChat()
        return current_chat if current_chat else None
    except Exception as e:
        return None

def safe_send_message(wx_instance, message: str, who: str = None) -> bool:
    """
    安全发送消息
    
    Args:
        wx_instance: 微信实例对象
        message: 要发送的消息
        who: 发送给谁，None表示当前聊天
        
    Returns:
        bool: 发送是否成功
    """
    if not wx_instance or not message:
        return False
    
    try:
        if who:
            # 发送给指定对象
            wx_instance.SendMsg(message, who)
        else:
            # 发送给当前聊天对象
            wx_instance.SendMsg(message)
        return True
    except Exception as e:
        print(f"发送消息失败: {e}")
        return False

def check_wx_instance_health(wx_instance) -> bool:
    """
    检查微信实例健康状态
    
    Args:
        wx_instance: 微信实例对象
        
    Returns:
        bool: 实例是否健康
    """
    if not wx_instance:
        return False
    
    try:
        # 检查基本属性
        if not hasattr(wx_instance, 'nickname'):
            return False
        
        # 检查昵称是否可获取
        nickname = wx_instance.nickname
        if not nickname:
            return False
        
        # 检查是否能获取当前聊天（不一定成功，但不应该抛出严重错误）
        try:
            wx_instance.CurrentChat()
        except Exception:
            # CurrentChat失败不一定表示实例不健康
            pass
        
        return True
    except Exception as e:
        return False

def get_message_info(msg) -> dict:
    """
    安全获取消息信息
    
    Args:
        msg: 消息对象
        
    Returns:
        dict: 消息信息字典
    """
    info = {
        'sender': '',
        'content': '',
        'type': 'unknown',
        'time': None
    }
    
    try:
        # 获取发送者
        info['sender'] = getattr(msg, 'sender', '') or getattr(msg, 'from', '') or ''
        
        # 获取内容
        info['content'] = getattr(msg, 'content', '') or getattr(msg, 'text', '') or ''
        
        # 获取类型
        if hasattr(msg, '__class__'):
            class_name = msg.__class__.__name__
            if 'Text' in class_name:
                info['type'] = 'text'
            elif 'Image' in class_name:
                info['type'] = 'image'
            elif 'File' in class_name:
                info['type'] = 'file'
            else:
                info['type'] = 'other'
        
        # 获取时间
        info['time'] = getattr(msg, 'time', None) or getattr(msg, 'timestamp', None)
        
    except Exception as e:
        # 获取信息失败，返回默认值
        pass
    
    return info
