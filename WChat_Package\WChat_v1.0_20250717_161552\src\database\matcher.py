"""
内容匹配模块
实现智能匹配算法，用于FAQ和产品信息的相似度计算
"""
import jieba
from fuzzywuzzy import fuzz, process
from typing import List, Dict, Tuple, Optional
from src.utils.logger import get_logger

logger = get_logger("matcher")


class ContentMatcher:
    """内容匹配器"""
    
    def __init__(self, similarity_threshold: float = 0.7):
        self.similarity_threshold = similarity_threshold
        # 初始化jieba分词
        jieba.initialize()
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        try:
            # 使用fuzzywuzzy计算相似度
            similarity = fuzz.ratio(text1.lower(), text2.lower()) / 100.0
            return similarity
        except Exception as e:
            logger.error(f"计算相似度失败: {e}")
            return 0.0
    
    def extract_keywords(self, text: str) -> List[str]:
        """
        提取文本关键词
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 关键词列表
        """
        try:
            # 使用jieba分词
            words = jieba.lcut(text)
            # 过滤掉长度小于2的词和常见停用词
            stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
            keywords = [word for word in words if len(word) >= 2 and word not in stop_words]
            return keywords
        except Exception as e:
            logger.error(f"提取关键词失败: {e}")
            return []
    
    def match_faq(self, question: str, faq_list: List[Dict]) -> List[Tuple[Dict, float]]:
        """
        匹配FAQ
        
        Args:
            question: 用户问题
            faq_list: FAQ列表
            
        Returns:
            List[Tuple[Dict, float]]: 匹配结果和相似度分数的列表
        """
        if not faq_list:
            return []
        
        matches = []
        question_keywords = self.extract_keywords(question)
        
        for faq in faq_list:
            try:
                # 计算与标准问题的相似度
                standard_question = str(faq.get('标准问题', ''))
                similarity1 = self.calculate_similarity(question, standard_question)
                
                # 计算与关键词的匹配度
                keywords = str(faq.get('问题关键词', '')).split(',')
                keyword_similarity = 0.0
                
                for keyword in keywords:
                    keyword = keyword.strip()
                    if keyword:
                        # 检查关键词是否在问题中
                        if keyword.lower() in question.lower():
                            keyword_similarity = max(keyword_similarity, 1.0)
                        else:
                            # 计算与问题关键词的相似度
                            for q_keyword in question_keywords:
                                sim = self.calculate_similarity(keyword, q_keyword)
                                keyword_similarity = max(keyword_similarity, sim)
                
                # 综合相似度（标准问题权重0.6，关键词权重0.4）
                final_similarity = similarity1 * 0.6 + keyword_similarity * 0.4
                
                if final_similarity >= self.similarity_threshold:
                    matches.append((faq, final_similarity))
                    
            except Exception as e:
                logger.error(f"匹配FAQ失败: {e}")
                continue
        
        # 按相似度降序排序
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches
    
    def match_products(self, query: str, product_list: List[Dict]) -> List[Tuple[Dict, float]]:
        """
        匹配产品
        
        Args:
            query: 查询关键词
            product_list: 产品列表
            
        Returns:
            List[Tuple[Dict, float]]: 匹配结果和相似度分数的列表
        """
        if not product_list:
            return []
        
        matches = []
        query_keywords = self.extract_keywords(query)
        
        for product in product_list:
            try:
                product_name = str(product.get('产品名称', ''))
                product_desc = str(product.get('产品描述', ''))
                
                # 计算与产品名称的相似度
                name_similarity = self.calculate_similarity(query, product_name)
                
                # 计算与产品描述的相似度
                desc_similarity = self.calculate_similarity(query, product_desc)
                
                # 计算关键词匹配度
                keyword_similarity = 0.0
                product_keywords = self.extract_keywords(product_name + ' ' + product_desc)
                
                for q_keyword in query_keywords:
                    for p_keyword in product_keywords:
                        sim = self.calculate_similarity(q_keyword, p_keyword)
                        keyword_similarity = max(keyword_similarity, sim)
                
                # 综合相似度（产品名称权重0.5，描述权重0.3，关键词权重0.2）
                final_similarity = name_similarity * 0.5 + desc_similarity * 0.3 + keyword_similarity * 0.2
                
                if final_similarity >= self.similarity_threshold:
                    matches.append((product, final_similarity))
                    
            except Exception as e:
                logger.error(f"匹配产品失败: {e}")
                continue
        
        # 按相似度降序排序
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches
    
    def find_best_match(self, query: str, candidates: List[str]) -> Optional[Tuple[str, float]]:
        """
        在候选列表中找到最佳匹配
        
        Args:
            query: 查询文本
            candidates: 候选文本列表
            
        Returns:
            Optional[Tuple[str, float]]: 最佳匹配和相似度分数
        """
        if not candidates:
            return None
        
        try:
            # 使用fuzzywuzzy的process.extractOne
            result = process.extractOne(query, candidates, scorer=fuzz.ratio)
            if result and result[1] >= self.similarity_threshold * 100:
                return result[0], result[1] / 100.0
        except Exception as e:
            logger.error(f"查找最佳匹配失败: {e}")
        
        return None
