{% extends "base.html" %}

{% block title %}基础配置 - 私域自动化{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog me-2"></i>
        基础配置
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary btn-sm" onclick="saveConfig()">
            <i class="fas fa-save me-1"></i>保存配置
        </button>
    </div>
</div>

<form id="configForm">
    <!-- 系统配置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-cogs me-2 text-primary"></i>
                系统配置
            </h5>
        </div>
        <div class="card-body">
            <!-- 监听模式选择 -->
            <div class="row mb-4">
                <div class="col-12">
                    <label class="form-label">监听模式</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="listen_mode" id="listen_mode_list"
                               value="list" {% if not config.wechat.listen_all %}checked{% endif %}>
                        <label class="btn btn-outline-primary" for="listen_mode_list">
                            <i class="fas fa-list me-2"></i>指定列表监听
                        </label>

                        <input type="radio" class="btn-check" name="listen_mode" id="listen_mode_all"
                               value="all" {% if config.wechat.listen_all %}checked{% endif %}>
                        <label class="btn btn-outline-success" for="listen_mode_all">
                            <i class="fas fa-globe me-2"></i>监听所有消息
                        </label>
                    </div>
                    <div class="form-text">
                        <strong>指定列表监听：</strong>只监听下方列表中的聊天对象<br>
                        <strong>监听所有消息：</strong>监听所有微信消息（除了自己发送的）
                    </div>

                    <!-- 当前状态显示 -->
                    <div class="alert alert-info mt-3" id="listen_mode_status">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="listen_mode_text">正在加载监听模式状态...</span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3" id="listen_list_container">
                        <label for="listen_list" class="form-label">
                            监听列表
                            <span class="badge bg-secondary ms-2" id="listen_list_count">
                                {{ config.wechat.listen_list|length }} 个对象
                            </span>
                        </label>
                        <textarea class="form-control" id="listen_list" rows="4"
                                  placeholder="每行一个聊天对象名称">{{ '\n'.join(config.wechat.listen_list) }}</textarea>
                        <div class="form-text">
                            输入要监听的微信聊天对象名称，每行一个<br>
                            <small class="text-muted">如：张三、客服群、产品讨论组</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="auto_reply" 
                                   {{ 'checked' if config.wechat.auto_reply else '' }}>
                            <label class="form-check-label" for="auto_reply">
                                启用自动回复
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="reply_delay" class="form-label">回复延迟（秒）</label>
                        <input type="number" class="form-control" id="reply_delay" 
                               value="{{ config.wechat.reply_delay }}" min="0" max="60">
                        <div class="form-text">发送回复前的等待时间</div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- AI配置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-brain me-2 text-info"></i>
                AI模型配置
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="ai_enabled" 
                                   {{ 'checked' if config.ai.enabled else '' }}>
                            <label class="form-check-label" for="ai_enabled">
                                启用AI回复
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="api_key" class="form-label">API密钥</label>
                        <input type="password" class="form-control" id="api_key" 
                               value="{{ config.ai.api_key }}" placeholder="输入API密钥">
                    </div>
                    <div class="mb-3">
                        <label for="base_url" class="form-label">API地址</label>
                        <input type="url" class="form-control" id="base_url" 
                               value="{{ config.ai.base_url }}" placeholder="https://api.deepseek.com">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="model" class="form-label">
                            AI模型选择
                            <span class="badge bg-info ms-2" id="model-status">当前模型</span>
                        </label>
                        <div class="input-group">
                            <select class="form-select" id="model">
                                <option value="{{ config.ai.model }}" selected>{{ config.ai.model }}</option>
                            </select>
                            <button class="btn btn-outline-secondary" type="button" id="refresh-models" onclick="loadAvailableModels()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div class="form-text">
                            <small id="model-info">选择要使用的AI模型</small>
                        </div>

                        <!-- 模型信息显示 -->
                        <div class="mt-2" id="model-details" style="display: none;">
                            <div class="alert alert-info py-2">
                                <small>
                                    <strong>当前模型：</strong><span id="current-model-name">{{ config.ai.model }}</span><br>
                                    <strong>可用模型：</strong><span id="available-models-count">加载中...</span>
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="max_tokens" class="form-label">最大令牌数</label>
                        <input type="number" class="form-control" id="max_tokens" 
                               value="{{ config.ai.max_tokens }}" min="100" max="4000">
                    </div>
                    <div class="mb-3">
                        <label for="temperature" class="form-label">温度参数</label>
                        <input type="number" class="form-control" id="temperature"
                               value="{{ config.ai.temperature }}" min="0" max="2" step="0.1">
                        <div class="form-text">控制回复的随机性，0-2之间</div>
                    </div>
                    <div class="mb-3">
                        <label for="system_prompt" class="form-label">AI系统提示词</label>
                        <textarea class="form-control" id="system_prompt" rows="8"
                                  placeholder="输入AI系统提示词，定义AI的角色和回复风格">{{ config.ai.system_prompt }}</textarea>
                        <div class="form-text">
                            定义AI的角色、职责和回复风格。这将影响AI的所有回复内容。
                            <br>提示：可以指定AI专注于产品推荐、客服问答等特定场景。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据库配置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-database me-2 text-warning"></i>
                数据库配置
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="faq_file" class="form-label">FAQ文件路径</label>
                        <input type="text" class="form-control" id="faq_file" 
                               value="{{ config.database.faq_file }}" placeholder="data/faq.xlsx">
                    </div>
                    <div class="mb-3">
                        <label for="products_file" class="form-label">产品文件路径</label>
                        <input type="text" class="form-control" id="products_file" 
                               value="{{ config.database.products_file }}" placeholder="data/products.xlsx">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="similarity_threshold" class="form-label">相似度阈值</label>
                        <input type="number" class="form-control" id="similarity_threshold" 
                               value="{{ config.database.similarity_threshold }}" min="0" max="1" step="0.1">
                        <div class="form-text">匹配内容的最低相似度要求</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 回复策略配置 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-reply me-2 text-primary"></i>
                回复策略配置
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="priority_faq" 
                                   {{ 'checked' if config.reply.priority_faq else '' }}>
                            <label class="form-check-label" for="priority_faq">
                                优先使用FAQ库
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="priority_products" 
                                   {{ 'checked' if config.reply.priority_products else '' }}>
                            <label class="form-check-label" for="priority_products">
                                优先使用产品库
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="use_ai_fallback" 
                                   {{ 'checked' if config.reply.use_ai_fallback else '' }}>
                            <label class="form-check-label" for="use_ai_fallback">
                                无匹配时使用AI
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="default_reply" class="form-label">默认回复内容</label>
                        <textarea class="form-control" id="default_reply" rows="4" 
                                  placeholder="当所有方式都无法回复时使用的默认内容">{{ config.reply.default_reply }}</textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
function saveConfig() {
    const configData = {
        wechat: {
            listen_list: $('#listen_list').val().split('\n').filter(line => line.trim()),
            listen_all: $('input[name="listen_mode"]:checked').val() === 'all',
            auto_reply: $('#auto_reply').is(':checked'),
            reply_delay: parseInt($('#reply_delay').val())
        },
        ai: {
            enabled: $('#ai_enabled').is(':checked'),
            api_key: $('#api_key').val(),
            base_url: $('#base_url').val(),
            model: $('#model').val(),
            max_tokens: parseInt($('#max_tokens').val()),
            temperature: parseFloat($('#temperature').val()),
            system_prompt: $('#system_prompt').val()
        },
        database: {
            faq_file: $('#faq_file').val(),
            products_file: $('#products_file').val(),
            similarity_threshold: parseFloat($('#similarity_threshold').val())
        },
        reply: {
            priority_faq: $('#priority_faq').is(':checked'),
            priority_products: $('#priority_products').is(':checked'),
            use_ai_fallback: $('#use_ai_fallback').is(':checked'),
            default_reply: $('#default_reply').val()
        }
    };

    // 显示保存中状态
    const saveBtn = $('button[onclick="saveConfig()"]');
    const originalText = saveBtn.html();
    saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>保存中...');

    $.ajax({
        url: '/api/save_config',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(configData),
        success: function(response) {
            showAlert(response.message, 'success');
            // 更新监听模式显示
            updateListenModeInfo();
        },
        error: function(xhr) {
            const error = xhr.responseJSON ? xhr.responseJSON.error : '保存失败';
            showAlert(error, 'danger');
        },
        complete: function() {
            // 恢复按钮状态
            saveBtn.prop('disabled', false).html(originalText);
        }
    });
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.main-content .pt-3').prepend(alertHtml);
}

// 监听模式切换
function toggleListenMode() {
    const isListMode = $('input[name="listen_mode"]:checked').val() === 'list';
    const container = $('#listen_list_container');

    if (isListMode) {
        container.show();
        container.find('.form-label').removeClass('text-muted');
        $('#listen_list').prop('disabled', false);
    } else {
        container.hide();
        container.find('.form-label').addClass('text-muted');
        $('#listen_list').prop('disabled', true);
    }

    updateListenModeInfo();
}

// 更新监听模式信息
function updateListenModeInfo() {
    const isListMode = $('input[name="listen_mode"]:checked').val() === 'list';
    const listCount = $('#listen_list').val().split('\n').filter(line => line.trim()).length;

    $('#listen_list_count').text(listCount + ' 个对象');

    // 更新提示信息
    if (isListMode) {
        if (listCount === 0) {
            $('#listen_list_count').removeClass('bg-secondary').addClass('bg-warning');
            $('#listen_list_count').text('列表为空 - 将监听所有消息');
        } else {
            $('#listen_list_count').removeClass('bg-warning').addClass('bg-secondary');
            $('#listen_list_count').text(listCount + ' 个对象');
        }
    }

    // 更新状态显示
    updateListenModeStatus();
}

// 更新监听模式状态显示
function updateListenModeStatus() {
    const isListMode = $('input[name="listen_mode"]:checked').val() === 'list';
    const listCount = $('#listen_list').val().split('\n').filter(line => line.trim()).length;
    const statusDiv = $('#listen_mode_status');
    const statusText = $('#listen_mode_text');

    if (isListMode) {
        if (listCount === 0) {
            statusDiv.removeClass('alert-info alert-success').addClass('alert-warning');
            statusText.html('<strong>当前模式：</strong>指定列表监听 <span class="badge bg-warning text-dark ms-2">列表为空</span><br>' +
                          '<small>由于监听列表为空，机器人将监听所有微信消息</small>');
        } else {
            statusDiv.removeClass('alert-info alert-warning').addClass('alert-success');
            statusText.html('<strong>当前模式：</strong>指定列表监听 <span class="badge bg-success ms-2">' + listCount + ' 个对象</span><br>' +
                          '<small>机器人只会监听列表中指定的聊天对象</small>');
        }
    } else {
        statusDiv.removeClass('alert-warning alert-success').addClass('alert-info');
        statusText.html('<strong>当前模式：</strong>监听所有消息 <span class="badge bg-primary ms-2">全局监听</span><br>' +
                      '<small>机器人将监听所有微信消息（除了自己发送的）</small>');
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    // 初始化监听模式显示
    toggleListenMode();

    // 监听模式切换事件
    $('input[name="listen_mode"]').change(function() {
        toggleListenMode();
    });

    // 监听列表内容变化事件
    $('#listen_list').on('input', function() {
        updateListenModeInfo();
    });

    // 添加快速操作按钮
    addQuickActions();
});

// 添加快速操作
function addQuickActions() {
    const quickActionsHtml = `
        <div class="mt-2">
            <small class="text-muted">快速操作：</small>
            <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="addCommonContacts()">
                <i class="fas fa-plus me-1"></i>添加常用联系人
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="clearListenList()">
                <i class="fas fa-trash me-1"></i>清空列表
            </button>
        </div>
    `;
    $('#listen_list_container').append(quickActionsHtml);
}

// 添加常用联系人
function addCommonContacts() {
    const commonContacts = ['客服群', '产品讨论组', '技术支持', '销售团队'];
    const currentList = $('#listen_list').val().split('\n').filter(line => line.trim());

    let newContacts = [];
    commonContacts.forEach(contact => {
        if (!currentList.includes(contact)) {
            newContacts.push(contact);
        }
    });

    if (newContacts.length > 0) {
        const updatedList = currentList.concat(newContacts).join('\n');
        $('#listen_list').val(updatedList);
        updateListenModeInfo();
        showAlert(`已添加 ${newContacts.length} 个常用联系人`, 'success');
    } else {
        showAlert('所有常用联系人都已在列表中', 'info');
    }
}

// 清空监听列表
function clearListenList() {
    if (confirm('确认清空监听列表？')) {
        $('#listen_list').val('');
        updateListenModeInfo();
        showAlert('监听列表已清空', 'success');
    }
}

// 加载可用模型
function loadAvailableModels() {
    const refreshBtn = $('#refresh-models');
    const originalHtml = refreshBtn.html();

    // 显示加载状态
    refreshBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
    $('#model-status').removeClass('bg-info bg-success bg-warning').addClass('bg-secondary').text('加载中...');

    $.ajax({
        url: '/api/available_models',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateModelSelect(response.models, response.current_model);
                $('#available-models-count').text(`${response.total_count} 个可用模型`);
                $('#model-details').show();
                $('#model-status').removeClass('bg-secondary bg-warning').addClass('bg-success').text('已加载');
                showAlert(`成功加载 ${response.total_count} 个可用模型`, 'success');
            } else {
                showAlert('加载模型失败: ' + response.error, 'danger');
                $('#model-status').removeClass('bg-secondary bg-success').addClass('bg-warning').text('加载失败');
            }
        },
        error: function(xhr) {
            const error = xhr.responseJSON ? xhr.responseJSON.error : '加载模型失败';
            showAlert('加载模型失败: ' + error, 'danger');
            $('#model-status').removeClass('bg-secondary bg-success').addClass('bg-warning').text('加载失败');
        },
        complete: function() {
            refreshBtn.prop('disabled', false).html(originalHtml);
        }
    });
}

// 更新模型选择器
function updateModelSelect(models, currentModel) {
    const modelSelect = $('#model');
    modelSelect.empty();

    // 添加模型选项
    models.forEach(function(model) {
        const option = $('<option></option>')
            .attr('value', model.id)
            .text(model.name);

        if (model.id === currentModel) {
            option.attr('selected', true);
        }

        modelSelect.append(option);
    });

    // 更新当前模型显示
    $('#current-model-name').text(currentModel);

    // 添加模型切换事件
    modelSelect.off('change').on('change', function() {
        const newModel = $(this).val();
        if (newModel !== currentModel) {
            switchModel(newModel);
        }
    });
}

// 切换模型
function switchModel(newModel) {
    if (!newModel) {
        showAlert('请选择一个模型', 'warning');
        return;
    }

    if (!confirm(`确认切换到模型: ${newModel}？`)) {
        // 用户取消，恢复原选择
        $('#model').val($('#current-model-name').text());
        return;
    }

    // 显示切换状态
    $('#model-status').removeClass('bg-success bg-warning').addClass('bg-info').text('切换中...');

    $.ajax({
        url: '/api/switch_model',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ model: newModel }),
        success: function(response) {
            if (response.success) {
                $('#current-model-name').text(response.new_model);
                $('#model-status').removeClass('bg-info bg-warning').addClass('bg-success').text('切换成功');
                showAlert(response.message, 'success');

                // 更新页面显示的模型值
                $('#model').val(response.new_model);
            } else {
                showAlert('切换模型失败: ' + response.error, 'danger');
                $('#model-status').removeClass('bg-info bg-success').addClass('bg-warning').text('切换失败');
                // 恢复原选择
                $('#model').val($('#current-model-name').text());
            }
        },
        error: function(xhr) {
            const error = xhr.responseJSON ? xhr.responseJSON.error : '切换模型失败';
            showAlert('切换模型失败: ' + error, 'danger');
            $('#model-status').removeClass('bg-info bg-success').addClass('bg-warning').text('切换失败');
            // 恢复原选择
            $('#model').val($('#current-model-name').text());
        }
    });
}

// 页面加载时自动加载模型列表
$(document).ready(function() {
    // 延迟加载模型列表，避免阻塞页面加载
    setTimeout(function() {
        loadAvailableModels();
    }, 1000);
});
</script>
{% endblock %}
