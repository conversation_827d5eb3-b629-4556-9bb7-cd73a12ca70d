2025-07-17 15:08:09 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:08:09 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:08:14 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:08:14 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:08:14 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:08:14 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-17 15:08:14 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-17 15:08:14 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-17 15:08:14 [main] [INFO] [run.py:150]  💡 提示:
2025-07-17 15:08:14 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-17 15:08:14 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-17 15:08:14 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-17 15:08:14 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:08:14 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:08:15 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:08:15 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:08:15 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:08:16 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:08:21 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:08:21 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:08:21 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:08:21 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:08:21 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:08:22 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:08:22 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:08:22 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:08:22 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:08:27 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:08:27 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:08:27 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:08:28 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:08:28 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:08:29 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:08:29 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:08:29 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:08:29 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:08:34 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:08:34 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:08:34 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:08:35 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:08:35 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:08:36 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:08:36 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:08:36 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:08:36 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:08:41 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:08:41 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:08:41 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:08:41 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:08:41 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:08:42 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:08:42 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:08:43 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:08:43 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:08:48 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:08:48 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:08:48 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:08:48 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:08:48 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:08:49 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:08:49 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:08:49 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:08:49 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:08:54 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:08:55 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:08:55 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:08:56 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:08:56 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:08:57 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:08:57 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:08:57 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:08:57 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:09:02 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:09:02 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:09:02 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:09:02 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:09:02 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:09:03 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:09:04 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:09:04 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:09:04 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:09:09 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:09:09 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:09:09 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:09:09 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:09:09 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:09:10 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:09:10 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:09:11 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:09:11 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:09:16 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:09:16 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:09:16 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:09:16 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:09:16 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:09:17 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:09:17 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:09:17 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:09:17 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:09:22 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:09:22 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:09:22 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:09:23 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:09:23 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:09:24 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:09:24 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:09:24 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:09:24 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:09:29 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:09:29 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:09:29 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:09:30 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:09:30 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:09:31 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:09:31 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:09:31 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:09:31 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:09:36 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:09:36 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:09:36 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:09:37 [wechat_handler] [DEBUG] [wechat_handler.py:361]  GetNewMessage失败: 'NoneType' object has no attribute 'get_new_msgs'
2025-07-17 15:09:37 [wechat_handler] [INFO] [wechat_handler.py:366]  检测到wxauto内部状态问题，尝试重新初始化...
2025-07-17 15:09:38 [wechat_handler] [WARNING] [wechat_handler.py:341]  微信连接丢失，尝试重新连接...
2025-07-17 15:09:38 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:09:38 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:09:38 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:09:42 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-17 15:13:33 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:13:38 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:43]  增强回复引擎（支持图片）初始化完成
2025-07-17 15:13:38 [wechat_handler] [INFO] [wechat_handler.py:61]  微信处理器初始化完成
2025-07-17 15:13:38 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:13:38 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:13:38 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:13:43 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:13:43 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:13:43 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:13:43 [wechat_handler] [INFO] [wechat_handler.py:137]  正在初始化微信连接... (尝试 1/5)
2025-07-17 15:13:43 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:13:43 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:13:48 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:13:48 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:13:48 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:13:48 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-17 15:13:48 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-17 15:13:59 [wechat_handler] [INFO] [wechat_handler.py:303]  停止监听微信消息
2025-07-17 15:15:47 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:15:47 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:15:52 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:15:52 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: Rocky
2025-07-17 15:15:52 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:15:52 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-17 15:15:52 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-17 15:15:52 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-17 15:15:52 [main] [INFO] [run.py:150]  💡 提示:
2025-07-17 15:15:52 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-17 15:15:52 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-17 15:15:52 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-17 15:16:04 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-17 15:16:04 [wechat_handler] [DEBUG] [wechat_handler.py:449]  跳过消息: system - 以下为新消息... (ID: 88ce39c4)
2025-07-17 15:16:04 [wechat_handler] [DEBUG] [wechat_handler.py:485]  全局监听模式，处理消息: 樂
2025-07-17 15:16:04 [wechat_handler] [INFO] [wechat_handler.py:444]  处理消息: 樂 - 你好... (ID: 48988d37)
2025-07-17 15:16:04 [wechat_handler] [DEBUG] [wechat_handler.py:514]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-17 15:16:04 [wechat_handler] [DEBUG] [wechat_handler.py:570]  消息解析结果 - 发送者: 樂, 内容: 你好..., 类型: text
2025-07-17 15:16:04 [wechat_handler] [INFO] [wechat_handler.py:602]  消息详细信息 - 类型: text, 内容: 你好, 发送者: 樂
2025-07-17 15:16:04 [wechat_handler] [INFO] [wechat_handler.py:603]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-17 15:16:04 [wechat_handler] [INFO] [wechat_handler.py:685]  收到消息 - 发送者: 樂, 内容: 你好...
2025-07-17 15:16:04 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: 你好
2025-07-17 15:16:06 [llm_service] [INFO] [llm_service.py:36]  LLM客户端初始化成功
2025-07-17 15:16:06 [openai._base_client] [DEBUG] [_base_client.py:482]  Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-3a304ed7-7fad-4f04-b40c-0893672cd86d', 'json_data': {'messages': [{'role': 'system', 'content': '你是一个热情友好的销售顾问，对各类产品都很了解。\n\n你的特点：\n- 说话自然随和，就像朋友聊天一样\n- 对产品很熟悉，能给出实用的建议\n- 会根据用户需求推荐合适的产品\n- 不会说自己是AI或机器人\n- 不会提到联系客服或人工服务\n\n回复风格：\n- 用口语化的表达，比如这款挺不错的、我觉得、建议你看看\n- 简洁明了，一般20到50字就够了\n- 语气轻松友好，避免过于正式\n- 如果不确定，可以说我了解的不多或这个我不太清楚\n- 重点突出产品的实用价值和使用体验\n\n记住：你就是一个懂产品的朋友，自然地和用户交流就好。'}, {'role': 'user', 'content': '你好'}], 'model': 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B', 'max_tokens': 50, 'temperature': 0.7}}
2025-07-17 15:16:06 [openai._base_client] [DEBUG] [_base_client.py:968]  Sending HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions
2025-07-17 15:16:06 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.started host='127.0.0.1' port=8081 local_address=None timeout=5.0 socket_options=None
2025-07-17 15:16:06 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000026F173EDB10>
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'CONNECT']>
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'CONNECT']>
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-17 15:16:06 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.started ssl_context=<ssl.SSLContext object at 0x0000026F15F6DAC0> server_hostname='api.siliconflow.cn' timeout=5.0
2025-07-17 15:16:06 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000026F173EF8D0>
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'POST']>
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'POST']>
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-17 15:16:06 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'POST']>
2025-07-17 15:16:28 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 17 Jul 2025 07:16:30 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Content-Length', b'1245'), (b'Connection', b'keep-alive')])
2025-07-17 15:16:28 [httpx] [INFO] [_client.py:1025]  HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-17 15:16:28 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.started request=<Request [b'POST']>
2025-07-17 15:16:28 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.complete
2025-07-17 15:16:28 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.started
2025-07-17 15:16:28 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.complete
2025-07-17 15:16:28 [openai._base_client] [DEBUG] [_base_client.py:1006]  HTTP Response: POST https://api.siliconflow.cn/v1/chat/completions "200 OK" Headers({'date': 'Thu, 17 Jul 2025 07:16:30 GMT', 'content-type': 'application/json; charset=utf-8', 'content-length': '1245', 'connection': 'keep-alive'})
2025-07-17 15:16:28 [openai._base_client] [DEBUG] [_base_client.py:1014]  request_id: None
2025-07-17 15:16:28 [llm_service] [INFO] [llm_service.py:91]  AI回复生成成功，长度: 42
2025-07-17 15:16:28 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:107]  使用AI回复
2025-07-17 15:16:30 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: 樂, False, False, 0.5
2025-07-17 15:16:32 [wechat_handler] [DEBUG] [wechat_handler.py:919]  消息已发送到: 樂
2025-07-17 15:16:32 [wechat_handler] [INFO] [wechat_handler.py:706]  已回复: 你好呀！很高兴见到你～有什么需要帮忙的吗？我这里有很多好东西哦，欢迎随时找我聊聊 😊... (图片: 0张)
2025-07-17 15:16:34 [wxauto] [DEBUG] [msg.py:80]  content: 你好呀！很高兴见到你～有什么需要帮忙的吗？我这里有很多好东西哦，欢迎随时找我聊聊 😊, length: 8
2025-07-17 15:16:34 [wechat_handler] [DEBUG] [wechat_handler.py:449]  跳过消息: self - 你好呀！很高兴见到你～有什么需要帮忙的吗？我这里有很多好东西... (ID: 2df26d82)
2025-07-17 15:19:45 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-17 15:19:45 [wechat_handler] [INFO] [wechat_handler.py:303]  停止监听微信消息
2025-07-17 15:19:45 [main] [INFO] [run.py:35]  程序已退出
2025-07-17 15:19:45 [main] [INFO] [run.py:176]  程序已退出
