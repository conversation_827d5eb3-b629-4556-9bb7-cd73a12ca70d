2025-07-17 15:46:56 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:46:56 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:47:01 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:47:01 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-17 15:47:01 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:47:01 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-17 15:47:01 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-17 15:47:01 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-17 15:47:01 [main] [INFO] [run.py:150]  💡 提示:
2025-07-17 15:47:01 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-17 15:47:01 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-17 15:47:01 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-17 15:50:06 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:50:06 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:50:11 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:50:11 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-17 15:50:11 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:50:11 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-17 15:50:11 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-17 15:50:11 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-17 15:50:11 [main] [INFO] [run.py:150]  💡 提示:
2025-07-17 15:50:11 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-17 15:50:11 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-17 15:50:11 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-17 15:51:28 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-17 15:51:41 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:51:41 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:51:46 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:51:46 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-17 15:51:46 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:51:46 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-17 15:51:46 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-17 15:51:46 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-17 15:51:46 [main] [INFO] [run.py:150]  💡 提示:
2025-07-17 15:51:46 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-17 15:51:46 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-17 15:51:46 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-17 15:52:34 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-17 15:52:34 [wechat_handler] [INFO] [wechat_handler.py:303]  停止监听微信消息
2025-07-17 15:52:34 [main] [INFO] [run.py:35]  程序已退出
2025-07-17 15:52:34 [main] [INFO] [run.py:176]  程序已退出
2025-07-17 15:52:43 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:52:43 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:52:48 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:52:48 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-17 15:52:48 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:52:48 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-17 15:52:48 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-17 15:52:48 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-17 15:52:48 [main] [INFO] [run.py:150]  💡 提示:
2025-07-17 15:52:48 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-17 15:52:48 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-17 15:52:48 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-17 15:54:09 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-17 15:56:02 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:56:02 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:56:07 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:56:07 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-17 15:56:07 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:56:07 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-17 15:56:07 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-17 15:56:07 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-17 15:56:07 [main] [INFO] [run.py:150]  💡 提示:
2025-07-17 15:56:07 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-17 15:56:07 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-17 15:56:07 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-17 15:56:13 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-17 15:56:13 [wechat_handler] [DEBUG] [wechat_handler.py:449]  跳过消息: system - 以下为新消息... (ID: c23cc6d5)
2025-07-17 15:56:13 [wechat_handler] [DEBUG] [wechat_handler.py:485]  全局监听模式，处理消息: Rocky
2025-07-17 15:56:13 [wechat_handler] [INFO] [wechat_handler.py:444]  处理消息: Rocky - 你好... (ID: 3dee5dea)
2025-07-17 15:56:13 [wechat_handler] [DEBUG] [wechat_handler.py:514]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-17 15:56:13 [wechat_handler] [DEBUG] [wechat_handler.py:570]  消息解析结果 - 发送者: Rocky, 内容: 你好..., 类型: text
2025-07-17 15:56:13 [wechat_handler] [INFO] [wechat_handler.py:602]  消息详细信息 - 类型: text, 内容: 你好, 发送者: Rocky
2025-07-17 15:56:13 [wechat_handler] [INFO] [wechat_handler.py:603]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-17 15:56:13 [wechat_handler] [INFO] [wechat_handler.py:685]  收到消息 - 发送者: Rocky, 内容: 你好...
2025-07-17 15:56:13 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: 你好
2025-07-17 15:56:16 [llm_service] [INFO] [llm_service.py:36]  LLM客户端初始化成功
2025-07-17 15:56:16 [openai._base_client] [DEBUG] [_base_client.py:482]  Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-2c21fc05-8161-4a7d-990d-3e02d0ff699b', 'json_data': {'messages': [{'role': 'system', 'content': '你是一个热情友好的销售顾问，对各类产品都很了解。\n\n你的特点：\n- 说话自然随和，就像朋友聊天一样\n- 对产品很熟悉，能给出实用的建议\n- 会根据用户需求推荐合适的产品\n- 不会说自己是AI或机器人\n- 不会提到联系客服或人工服务\n\n回复风格：\n- 用口语化的表达，比如这款挺不错的、我觉得、建议你看看\n- 简洁明了，一般20到50字就够了\n- 语气轻松友好，避免过于正式\n- 如果不确定，可以说我了解的不多或这个我不太清楚\n- 重点突出产品的实用价值和使用体验\n\n记住：你就是一个懂产品的朋友，自然地和用户交流就好。'}, {'role': 'user', 'content': '你好'}], 'model': 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B', 'max_tokens': 50, 'temperature': 0.7}}
2025-07-17 15:56:16 [openai._base_client] [DEBUG] [_base_client.py:968]  Sending HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions
2025-07-17 15:56:16 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.started host='127.0.0.1' port=8081 local_address=None timeout=5.0 socket_options=None
2025-07-17 15:56:16 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001E92674D710>
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'CONNECT']>
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'CONNECT']>
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-17 15:56:16 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.started ssl_context=<ssl.SSLContext object at 0x000001E9252D16D0> server_hostname='api.siliconflow.cn' timeout=5.0
2025-07-17 15:56:16 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001E92674F450>
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'POST']>
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'POST']>
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-17 15:56:16 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'POST']>
2025-07-17 15:56:18 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 17 Jul 2025 07:56:20 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Content-Length', b'1196'), (b'Connection', b'keep-alive')])
2025-07-17 15:56:18 [httpx] [INFO] [_client.py:1025]  HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-17 15:56:18 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.started request=<Request [b'POST']>
2025-07-17 15:56:18 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.complete
2025-07-17 15:56:18 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.started
2025-07-17 15:56:18 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.complete
2025-07-17 15:56:18 [openai._base_client] [DEBUG] [_base_client.py:1006]  HTTP Response: POST https://api.siliconflow.cn/v1/chat/completions "200 OK" Headers({'date': 'Thu, 17 Jul 2025 07:56:20 GMT', 'content-type': 'application/json; charset=utf-8', 'content-length': '1196', 'connection': 'keep-alive'})
2025-07-17 15:56:18 [openai._base_client] [DEBUG] [_base_client.py:1014]  request_id: None
2025-07-17 15:56:18 [llm_service] [INFO] [llm_service.py:91]  AI回复生成成功，长度: 22
2025-07-17 15:56:18 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:107]  使用AI回复
2025-07-17 15:56:20 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: Rocky, False, False, 0.5
2025-07-17 15:56:22 [wechat_handler] [DEBUG] [wechat_handler.py:919]  消息已发送到: Rocky
2025-07-17 15:56:22 [wechat_handler] [INFO] [wechat_handler.py:706]  已回复: 你好啊！很高兴为你服务，有什么需要帮忙的吗？... (图片: 0张)
2025-07-17 15:56:24 [wxauto] [DEBUG] [msg.py:80]  content: 你好啊！很高兴为你服务，有什么需要帮忙的吗？, length: 8
2025-07-17 15:56:24 [wechat_handler] [DEBUG] [wechat_handler.py:449]  跳过消息: self - 你好啊！很高兴为你服务，有什么需要帮忙的吗？... (ID: d52a773c)
2025-07-17 15:58:39 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-17 15:58:39 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-17 15:58:44 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-17 15:58:44 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-17 15:58:44 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-17 15:58:44 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-17 15:58:44 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-17 15:58:44 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-17 15:58:44 [main] [INFO] [run.py:150]  💡 提示:
2025-07-17 15:58:44 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-17 15:58:44 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-17 15:58:44 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-17 15:58:55 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-17 15:58:55 [wechat_handler] [DEBUG] [wechat_handler.py:449]  跳过消息: system - 以下为新消息... (ID: 16974a9c)
2025-07-17 15:58:55 [wechat_handler] [DEBUG] [wechat_handler.py:485]  全局监听模式，处理消息: Rocky
2025-07-17 15:58:55 [wechat_handler] [INFO] [wechat_handler.py:444]  处理消息: Rocky - 你好... (ID: e4b558f8)
2025-07-17 15:58:55 [wechat_handler] [DEBUG] [wechat_handler.py:514]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-17 15:58:55 [wechat_handler] [DEBUG] [wechat_handler.py:570]  消息解析结果 - 发送者: Rocky, 内容: 你好..., 类型: text
2025-07-17 15:58:55 [wechat_handler] [INFO] [wechat_handler.py:602]  消息详细信息 - 类型: text, 内容: 你好, 发送者: Rocky
2025-07-17 15:58:55 [wechat_handler] [INFO] [wechat_handler.py:603]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-17 15:58:55 [wechat_handler] [INFO] [wechat_handler.py:685]  收到消息 - 发送者: Rocky, 内容: 你好...
2025-07-17 15:58:55 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: 你好
2025-07-17 15:58:57 [llm_service] [INFO] [llm_service.py:36]  LLM客户端初始化成功
2025-07-17 15:58:57 [openai._base_client] [DEBUG] [_base_client.py:482]  Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-81688f55-afe2-4892-a64c-8628f40769ae', 'json_data': {'messages': [{'role': 'system', 'content': '你是一个热情友好的销售顾问，对各类产品都很了解。\n\n你的特点：\n- 说话自然随和，就像朋友聊天一样\n- 对产品很熟悉，能给出实用的建议\n- 会根据用户需求推荐合适的产品\n- 不会说自己是AI或机器人\n- 不会提到联系客服或人工服务\n\n回复风格：\n- 用口语化的表达，比如这款挺不错的、我觉得、建议你看看\n- 简洁明了，一般20到50字就够了\n- 语气轻松友好，避免过于正式\n- 如果不确定，可以说我了解的不多或这个我不太清楚\n- 重点突出产品的实用价值和使用体验\n\n记住：你就是一个懂产品的朋友，自然地和用户交流就好。'}, {'role': 'user', 'content': '你好'}], 'model': 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B', 'max_tokens': 50, 'temperature': 0.7}}
2025-07-17 15:58:57 [openai._base_client] [DEBUG] [_base_client.py:968]  Sending HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions
2025-07-17 15:58:57 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.started host='127.0.0.1' port=8081 local_address=None timeout=5.0 socket_options=None
2025-07-17 15:58:57 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000025EE24FDA50>
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'CONNECT']>
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'CONNECT']>
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-17 15:58:57 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.started ssl_context=<ssl.SSLContext object at 0x0000025EE10896D0> server_hostname='api.siliconflow.cn' timeout=5.0
2025-07-17 15:58:57 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000025EE24FF810>
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'POST']>
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'POST']>
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-17 15:58:57 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'POST']>
2025-07-17 15:59:12 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 17 Jul 2025 07:59:15 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Content-Length', b'1284'), (b'Connection', b'keep-alive')])
2025-07-17 15:59:12 [httpx] [INFO] [_client.py:1025]  HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-17 15:59:12 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.started request=<Request [b'POST']>
2025-07-17 15:59:12 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.complete
2025-07-17 15:59:12 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.started
2025-07-17 15:59:12 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.complete
2025-07-17 15:59:12 [openai._base_client] [DEBUG] [_base_client.py:1006]  HTTP Response: POST https://api.siliconflow.cn/v1/chat/completions "200 OK" Headers({'date': 'Thu, 17 Jul 2025 07:59:15 GMT', 'content-type': 'application/json; charset=utf-8', 'content-length': '1284', 'connection': 'keep-alive'})
2025-07-17 15:59:12 [openai._base_client] [DEBUG] [_base_client.py:1014]  request_id: None
2025-07-17 15:59:12 [llm_service] [INFO] [llm_service.py:91]  AI回复生成成功，长度: 40
2025-07-17 15:59:12 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:107]  使用AI回复
2025-07-17 15:59:14 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: Rocky, False, False, 0.5
2025-07-17 15:59:17 [wechat_handler] [DEBUG] [wechat_handler.py:919]  消息已发送到: Rocky
2025-07-17 15:59:17 [wechat_handler] [INFO] [wechat_handler.py:706]  已回复: 你好呀！我是小王，很高兴为你服务。今天想看看什么产品呢？还是有什么我可以帮你的？... (图片: 0张)
2025-07-17 15:59:19 [wxauto] [DEBUG] [msg.py:80]  content: 你好呀！我是小王，很高兴为你服务。今天想看看什么产品呢？还是有什么我可以帮你的？, length: 8
2025-07-17 15:59:19 [wechat_handler] [DEBUG] [wechat_handler.py:449]  跳过消息: self - 你好呀！我是小王，很高兴为你服务。今天想看看什么产品呢？还是... (ID: 8ef32449)
2025-07-17 15:59:21 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
