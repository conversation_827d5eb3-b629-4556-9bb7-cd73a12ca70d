# WChat 微信智能客服机器人

## 📋 项目简介

WChat是一个基于Python开发的微信智能客服机器人，支持：
- 🤖 智能FAQ问答
- 📦 产品信息推荐
- 🧠 AI辅助回复
- 🌐 Web配置界面
- 📊 数据管理功能

## 🚀 快速开始

### 1. 系统要求
- Windows 10/11
- Python 3.8或更高版本
- 微信PC版3.9.12（推荐）

### 2. 安装步骤
1. **解压文件包**到任意目录
2. **双击运行"安装.bat"**安装依赖包
3. **启动微信PC版并登录**
4. **双击"启动WChat.bat"**启动机器人

### 3. 首次配置
1. **双击"配置界面.bat"**打开Web配置
2. **访问**: http://127.0.0.1:5000
3. **登录密码**: admin123
4. **配置AI API密钥**（必需）
5. **添加监听列表**（可选，默认全局监听）

## ⚙️ 详细配置

### AI配置
1. 在Web界面进入"AI配置"页面
2. 选择AI服务商（推荐SiliconFlow）
3. 输入API密钥
4. 选择模型（推荐DeepSeek系列）

### 微信配置
1. 在Web界面进入"微信配置"页面
2. 设置自动回复开关
3. 配置回复延迟时间
4. 添加要监听的聊天对象（留空则全局监听）

### 数据管理
1. **FAQ管理**: 添加常见问题和答案
2. **产品管理**: 添加产品信息和图片
3. **数据导入**: 支持Excel文件批量导入

## 📁 文件结构

```
WChat/
├── 安装.bat                    # 安装脚本
├── 启动WChat.bat               # 启动机器人
├── 配置界面.bat                # Web配置界面
├── README.md                   # 使用说明
├── requirements.txt            # 依赖包列表
├── run.py                     # 主程序
├── web_config.py              # Web配置程序
├── config/                    # 配置文件目录
│   └── config.json           # 主配置文件
├── data/                     # 数据目录
│   ├── faq.xlsx             # FAQ数据
│   ├── products.xlsx        # 产品数据
│   ├── images/              # 产品图片
│   └── logs/                # 日志文件
└── src/                     # 源代码目录
    ├── ai/                  # AI服务
    ├── bot/                 # 机器人核心
    ├── database/            # 数据处理
    ├── utils/               # 工具类
    └── web/                 # Web界面
```

## 🔧 常见问题

### Q: 微信连接失败怎么办？
A: 
1. 确保使用微信PC版3.9.12
2. 确保微信已完全登录
3. 尝试重启微信和程序
4. 检查是否有安全软件阻止

### Q: AI回复不工作？
A:
1. 检查API密钥是否正确配置
2. 检查网络连接
3. 确认API服务商账户余额

### Q: 无法监听消息？
A:
1. 检查监听列表配置
2. 确认微信窗口状态
3. 查看日志文件排查问题

### Q: 如何更新FAQ和产品数据？
A:
1. 使用Web界面在线编辑
2. 直接编辑Excel文件
3. 通过API批量导入

## 📞 技术支持

### 日志文件位置
- 程序日志: `data/logs/`
- 错误信息: 查看命令行输出

### 配置文件位置
- 主配置: `config/config.json`
- 数据文件: `data/` 目录

### 常用命令
```bash
# 启动机器人
python run.py

# 启动配置界面
python web_config.py

# 检查依赖
pip list | findstr wxauto

# 重新安装依赖
pip install -r requirements.txt
```

## 📝 版本信息

- **版本**: v1.0
- **发布日期**: 2025-07-17
- **Python版本**: 3.8+
- **微信版本**: 3.9.12（推荐）

## ⚠️ 重要提示

1. **微信版本**: 强烈推荐使用微信PC版3.9.12，新版本可能存在兼容性问题
2. **API密钥**: 首次使用必须配置AI API密钥才能使用智能回复功能
3. **网络环境**: 需要稳定的网络连接访问AI服务
4. **数据备份**: 建议定期备份配置文件和数据文件

---

**祝你使用愉快！** 🎉
