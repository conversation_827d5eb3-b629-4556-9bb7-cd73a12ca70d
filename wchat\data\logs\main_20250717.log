2025-07-17 13:38:45,107 - main - INFO - 启动微信客服机器人...
2025-07-17 13:38:45,107 - main - INFO - 检查依赖和配置...
2025-07-17 13:38:45,108 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 13:38:45,109 - main - INFO - 依赖检查完成
2025-07-17 13:38:45,109 - main - INFO - ============================================================
2025-07-17 13:38:45,110 - main - INFO - 微信客服机器人状态
2025-07-17 13:38:45,110 - main - INFO - ============================================================
2025-07-17 13:38:45,111 - main - INFO - 监听列表: []
2025-07-17 13:38:45,111 - main - INFO - 自动回复: 启用
2025-07-17 13:38:45,112 - main - INFO - 回复延迟: 2秒
2025-07-17 13:38:45,113 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 13:38:45,113 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 13:38:45,114 - main - INFO - 相似度阈值: 0.7
2025-07-17 13:38:45,114 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 13:38:45,115 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 13:38:45,116 - main - INFO - API密钥: 已配置
2025-07-17 13:38:45,116 - main - INFO - ============================================================
2025-07-17 13:38:45,466 - main - INFO - 正在启动微信消息监听...
2025-07-17 13:38:51,476 - main - ERROR - ❌ 微信客服机器人启动失败！
2025-07-17 13:38:51,477 - main - ERROR - 请检查:
2025-07-17 13:38:51,480 - main - ERROR -    1. 微信PC版是否已登录
2025-07-17 13:38:51,481 - main - ERROR -    2. 监听列表是否正确配置
2025-07-17 13:38:51,481 - main - ERROR -    3. 网络连接是否正常
2025-07-17 13:38:51,482 - main - INFO - 程序已退出
2025-07-17 13:40:18,507 - main - INFO - 启动微信客服机器人...
2025-07-17 13:40:18,507 - main - INFO - 检查依赖和配置...
2025-07-17 13:40:18,508 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 13:40:18,508 - main - INFO - 依赖检查完成
2025-07-17 13:40:18,509 - main - INFO - ============================================================
2025-07-17 13:40:18,509 - main - INFO - 微信客服机器人状态
2025-07-17 13:40:18,510 - main - INFO - ============================================================
2025-07-17 13:40:18,510 - main - INFO - 监听列表: []
2025-07-17 13:40:18,510 - main - INFO - 自动回复: 启用
2025-07-17 13:40:18,511 - main - INFO - 回复延迟: 2秒
2025-07-17 13:40:18,511 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 13:40:18,512 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 13:40:18,512 - main - INFO - 相似度阈值: 0.7
2025-07-17 13:40:18,513 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 13:40:18,513 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 13:40:18,514 - main - INFO - API密钥: 已配置
2025-07-17 13:40:18,514 - main - INFO - ============================================================
2025-07-17 13:40:18,753 - main - INFO - 正在启动微信消息监听...
2025-07-17 13:40:24,767 - main - ERROR - ❌ 微信客服机器人启动失败！
2025-07-17 13:40:24,767 - main - ERROR - 请检查:
2025-07-17 13:40:24,768 - main - ERROR -    1. 微信PC版是否已登录
2025-07-17 13:40:24,770 - main - ERROR -    2. 监听列表是否正确配置
2025-07-17 13:40:24,771 - main - ERROR -    3. 网络连接是否正常
2025-07-17 13:40:24,771 - main - INFO - 程序已退出
2025-07-17 13:41:19,447 - main - INFO - 启动微信客服机器人...
2025-07-17 13:41:19,447 - main - INFO - 检查依赖和配置...
2025-07-17 13:41:19,448 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 13:41:19,449 - main - INFO - 依赖检查完成
2025-07-17 13:41:19,449 - main - INFO - ============================================================
2025-07-17 13:41:19,450 - main - INFO - 微信客服机器人状态
2025-07-17 13:41:19,450 - main - INFO - ============================================================
2025-07-17 13:41:19,451 - main - INFO - 监听列表: []
2025-07-17 13:41:19,451 - main - INFO - 自动回复: 启用
2025-07-17 13:41:19,452 - main - INFO - 回复延迟: 2秒
2025-07-17 13:41:19,452 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 13:41:19,453 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 13:41:19,454 - main - INFO - 相似度阈值: 0.7
2025-07-17 13:41:19,455 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 13:41:19,456 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 13:41:19,456 - main - INFO - API密钥: 已配置
2025-07-17 13:41:19,457 - main - INFO - ============================================================
2025-07-17 13:41:19,714 - main - INFO - 正在启动微信消息监听...
2025-07-17 13:41:25,724 - main - ERROR - ❌ 微信客服机器人启动失败！
2025-07-17 13:41:25,725 - main - ERROR - 请检查:
2025-07-17 13:41:25,727 - main - ERROR -    1. 微信PC版是否已登录
2025-07-17 13:41:25,728 - main - ERROR -    2. 监听列表是否正确配置
2025-07-17 13:41:25,729 - main - ERROR -    3. 网络连接是否正常
2025-07-17 13:41:25,729 - main - INFO - 程序已退出
2025-07-17 13:56:47,787 - main - INFO - 启动微信客服机器人...
2025-07-17 13:56:47,789 - main - INFO - 检查依赖和配置...
2025-07-17 13:56:47,792 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 13:56:47,792 - main - INFO - 依赖检查完成
2025-07-17 13:56:47,793 - main - INFO - ============================================================
2025-07-17 13:56:47,793 - main - INFO - 微信客服机器人状态
2025-07-17 13:56:47,794 - main - INFO - ============================================================
2025-07-17 13:56:47,794 - main - INFO - 监听列表: []
2025-07-17 13:56:47,795 - main - INFO - 自动回复: 启用
2025-07-17 13:56:47,795 - main - INFO - 回复延迟: 2秒
2025-07-17 13:56:47,796 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 13:56:47,796 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 13:56:47,797 - main - INFO - 相似度阈值: 0.7
2025-07-17 13:56:47,797 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 13:56:47,798 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 13:56:47,799 - main - INFO - API密钥: 已配置
2025-07-17 13:56:47,799 - main - INFO - ============================================================
2025-07-17 13:56:48,036 - main - INFO - 正在启动微信消息监听...
2025-07-17 13:57:09,295 - main - ERROR - ❌ 微信客服机器人启动失败！
2025-07-17 13:57:09,295 - main - ERROR - 请检查:
2025-07-17 13:57:09,296 - main - ERROR -    1. 微信PC版是否已登录
2025-07-17 13:57:09,296 - main - ERROR -    2. 监听列表是否正确配置
2025-07-17 13:57:09,296 - main - ERROR -    3. 网络连接是否正常
2025-07-17 13:57:09,297 - main - INFO - 程序已退出
2025-07-17 14:00:57,389 - main - INFO - 启动微信客服机器人...
2025-07-17 14:00:57,390 - main - INFO - 检查依赖和配置...
2025-07-17 14:00:57,391 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 14:00:57,391 - main - INFO - 依赖检查完成
2025-07-17 14:00:57,392 - main - INFO - ============================================================
2025-07-17 14:00:57,392 - main - INFO - 微信客服机器人状态
2025-07-17 14:00:57,393 - main - INFO - ============================================================
2025-07-17 14:00:57,393 - main - INFO - 监听列表: []
2025-07-17 14:00:57,394 - main - INFO - 自动回复: 启用
2025-07-17 14:00:57,395 - main - INFO - 回复延迟: 2秒
2025-07-17 14:00:57,395 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 14:00:57,396 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 14:00:57,396 - main - INFO - 相似度阈值: 0.7
2025-07-17 14:00:57,397 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 14:00:57,397 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 14:00:57,397 - main - INFO - API密钥: 已配置
2025-07-17 14:00:57,398 - main - INFO - ============================================================
2025-07-17 14:00:57,663 - main - INFO - 正在启动微信消息监听...
2025-07-17 14:01:03,975 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-17 14:01:03,985 - main - INFO - 程序已退出
2025-07-17 14:01:03,986 - main - INFO - 程序已退出
2025-07-17 14:08:40,998 - main - INFO - 启动微信客服机器人...
2025-07-17 14:08:40,998 - main - INFO - 检查依赖和配置...
2025-07-17 14:08:40,999 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 14:08:40,999 - main - INFO - 依赖检查完成
2025-07-17 14:08:41,000 - main - INFO - ============================================================
2025-07-17 14:08:41,000 - main - INFO - 微信客服机器人状态
2025-07-17 14:08:41,001 - main - INFO - ============================================================
2025-07-17 14:08:41,001 - main - INFO - 监听列表: []
2025-07-17 14:08:41,002 - main - INFO - 自动回复: 启用
2025-07-17 14:08:41,002 - main - INFO - 回复延迟: 2秒
2025-07-17 14:08:41,003 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 14:08:41,003 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 14:08:41,004 - main - INFO - 相似度阈值: 0.7
2025-07-17 14:08:41,004 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 14:08:41,005 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 14:08:41,005 - main - INFO - API密钥: 已配置
2025-07-17 14:08:41,006 - main - INFO - ============================================================
2025-07-17 14:08:41,252 - main - INFO - 正在启动微信消息监听...
2025-07-17 14:09:01,766 - main - ERROR - ❌ 微信客服机器人启动失败！
2025-07-17 14:09:01,767 - main - ERROR - 请检查:
2025-07-17 14:09:01,767 - main - ERROR -    1. 微信PC版是否已登录
2025-07-17 14:09:01,768 - main - ERROR -    2. 监听列表是否正确配置
2025-07-17 14:09:01,768 - main - ERROR -    3. 网络连接是否正常
2025-07-17 14:09:01,769 - main - INFO - 程序已退出
2025-07-17 15:08:08,432 - main - INFO - 启动微信客服机器人...
2025-07-17 15:08:08,433 - main - INFO - 检查依赖和配置...
2025-07-17 15:08:08,435 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 15:08:08,435 - main - INFO - 依赖检查完成
2025-07-17 15:08:08,436 - main - INFO - ============================================================
2025-07-17 15:08:08,437 - main - INFO - 微信客服机器人状态
2025-07-17 15:08:08,438 - main - INFO - ============================================================
2025-07-17 15:08:08,439 - main - INFO - 监听列表: []
2025-07-17 15:08:08,440 - main - INFO - 自动回复: 启用
2025-07-17 15:08:08,441 - main - INFO - 回复延迟: 2秒
2025-07-17 15:08:08,442 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 15:08:08,443 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 15:08:08,444 - main - INFO - 相似度阈值: 0.7
2025-07-17 15:08:08,445 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 15:08:08,445 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 15:08:08,446 - main - INFO - API密钥: 已配置
2025-07-17 15:08:08,453 - main - INFO - ============================================================
2025-07-17 15:08:08,711 - main - INFO - 正在启动微信消息监听...
2025-07-17 15:08:14,249 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-17 15:08:14,251 - main - INFO - 💡 提示:
2025-07-17 15:08:14,252 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-17 15:08:14,253 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-17 15:08:14,254 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-17 15:09:42,107 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-17 15:15:46,557 - main - INFO - 启动微信客服机器人...
2025-07-17 15:15:46,558 - main - INFO - 检查依赖和配置...
2025-07-17 15:15:46,558 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 15:15:46,559 - main - INFO - 依赖检查完成
2025-07-17 15:15:46,559 - main - INFO - ============================================================
2025-07-17 15:15:46,559 - main - INFO - 微信客服机器人状态
2025-07-17 15:15:46,560 - main - INFO - ============================================================
2025-07-17 15:15:46,560 - main - INFO - 监听列表: []
2025-07-17 15:15:46,561 - main - INFO - 自动回复: 启用
2025-07-17 15:15:46,561 - main - INFO - 回复延迟: 2秒
2025-07-17 15:15:46,562 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 15:15:46,562 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 15:15:46,563 - main - INFO - 相似度阈值: 0.7
2025-07-17 15:15:46,563 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 15:15:46,564 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 15:15:46,565 - main - INFO - API密钥: 已配置
2025-07-17 15:15:46,565 - main - INFO - ============================================================
2025-07-17 15:15:46,824 - main - INFO - 正在启动微信消息监听...
2025-07-17 15:15:52,434 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-17 15:15:52,436 - main - INFO - 💡 提示:
2025-07-17 15:15:52,439 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-17 15:15:52,442 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-17 15:15:52,444 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-17 15:19:45,723 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-17 15:19:45,926 - main - INFO - 程序已退出
2025-07-17 15:19:45,927 - main - INFO - 程序已退出
2025-07-17 15:25:12,812 - main - INFO - 启动微信客服机器人...
2025-07-17 15:25:12,813 - main - INFO - 检查依赖和配置...
2025-07-17 15:25:12,815 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 15:25:12,815 - main - INFO - 依赖检查完成
2025-07-17 15:25:12,816 - main - INFO - ============================================================
2025-07-17 15:25:12,817 - main - INFO - 微信客服机器人状态
2025-07-17 15:25:12,818 - main - INFO - ============================================================
2025-07-17 15:25:12,819 - main - INFO - 监听列表: []
2025-07-17 15:25:12,820 - main - INFO - 自动回复: 启用
2025-07-17 15:25:12,821 - main - INFO - 回复延迟: 2秒
2025-07-17 15:25:12,822 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 15:25:12,823 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 15:25:12,824 - main - INFO - 相似度阈值: 0.7
2025-07-17 15:25:12,827 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 15:25:12,828 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 15:25:12,829 - main - INFO - API密钥: 已配置
2025-07-17 15:25:12,830 - main - INFO - ============================================================
2025-07-17 15:25:13,087 - main - INFO - 正在启动微信消息监听...
2025-07-17 15:25:13,445 - main - ERROR - ❌ 微信客服机器人启动失败！
2025-07-17 15:25:13,446 - main - ERROR - 请检查:
2025-07-17 15:25:13,447 - main - ERROR -    1. 微信PC版是否已登录
2025-07-17 15:25:13,448 - main - ERROR -    2. 监听列表是否正确配置
2025-07-17 15:25:13,449 - main - ERROR -    3. 网络连接是否正常
2025-07-17 15:25:13,450 - main - INFO - 程序已退出
2025-07-17 15:46:55,336 - main - INFO - 启动微信客服机器人...
2025-07-17 15:46:55,337 - main - INFO - 检查依赖和配置...
2025-07-17 15:46:55,337 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 15:46:55,338 - main - INFO - 依赖检查完成
2025-07-17 15:46:55,338 - main - INFO - ============================================================
2025-07-17 15:46:55,338 - main - INFO - 微信客服机器人状态
2025-07-17 15:46:55,339 - main - INFO - ============================================================
2025-07-17 15:46:55,339 - main - INFO - 监听列表: []
2025-07-17 15:46:55,340 - main - INFO - 自动回复: 启用
2025-07-17 15:46:55,341 - main - INFO - 回复延迟: 2秒
2025-07-17 15:46:55,342 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 15:46:55,343 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 15:46:55,343 - main - INFO - 相似度阈值: 0.7
2025-07-17 15:46:55,344 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 15:46:55,344 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 15:46:55,345 - main - INFO - API密钥: 已配置
2025-07-17 15:46:55,346 - main - INFO - ============================================================
2025-07-17 15:46:55,613 - main - INFO - 正在启动微信消息监听...
2025-07-17 15:47:01,161 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-17 15:47:01,161 - main - INFO - 💡 提示:
2025-07-17 15:47:01,163 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-17 15:47:01,164 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-17 15:47:01,165 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-17 15:50:05,760 - main - INFO - 启动微信客服机器人...
2025-07-17 15:50:05,761 - main - INFO - 检查依赖和配置...
2025-07-17 15:50:05,762 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 15:50:05,763 - main - INFO - 依赖检查完成
2025-07-17 15:50:05,764 - main - INFO - ============================================================
2025-07-17 15:50:05,765 - main - INFO - 微信客服机器人状态
2025-07-17 15:50:05,766 - main - INFO - ============================================================
2025-07-17 15:50:05,767 - main - INFO - 监听列表: []
2025-07-17 15:50:05,767 - main - INFO - 自动回复: 启用
2025-07-17 15:50:05,778 - main - INFO - 回复延迟: 2秒
2025-07-17 15:50:05,779 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 15:50:05,781 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 15:50:05,783 - main - INFO - 相似度阈值: 0.7
2025-07-17 15:50:05,785 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 15:50:05,787 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 15:50:05,788 - main - INFO - API密钥: 已配置
2025-07-17 15:50:05,789 - main - INFO - ============================================================
2025-07-17 15:50:06,016 - main - INFO - 正在启动微信消息监听...
2025-07-17 15:50:11,547 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-17 15:50:11,550 - main - INFO - 💡 提示:
2025-07-17 15:50:11,552 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-17 15:50:11,553 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-17 15:50:11,554 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-17 15:51:28,357 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-17 15:51:40,904 - main - INFO - 启动微信客服机器人...
2025-07-17 15:51:40,905 - main - INFO - 检查依赖和配置...
2025-07-17 15:51:40,907 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 15:51:40,907 - main - INFO - 依赖检查完成
2025-07-17 15:51:40,908 - main - INFO - ============================================================
2025-07-17 15:51:40,909 - main - INFO - 微信客服机器人状态
2025-07-17 15:51:40,910 - main - INFO - ============================================================
2025-07-17 15:51:40,911 - main - INFO - 监听列表: []
2025-07-17 15:51:40,912 - main - INFO - 自动回复: 启用
2025-07-17 15:51:40,913 - main - INFO - 回复延迟: 2秒
2025-07-17 15:51:40,913 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 15:51:40,916 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 15:51:40,918 - main - INFO - 相似度阈值: 0.7
2025-07-17 15:51:40,919 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 15:51:40,920 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 15:51:40,921 - main - INFO - API密钥: 已配置
2025-07-17 15:51:40,922 - main - INFO - ============================================================
2025-07-17 15:51:41,170 - main - INFO - 正在启动微信消息监听...
2025-07-17 15:51:46,652 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-17 15:51:46,654 - main - INFO - 💡 提示:
2025-07-17 15:51:46,655 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-17 15:51:46,657 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-17 15:51:46,658 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-17 15:52:34,468 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-17 15:52:34,481 - main - INFO - 程序已退出
2025-07-17 15:52:34,482 - main - INFO - 程序已退出
2025-07-17 15:52:42,320 - main - INFO - 启动微信客服机器人...
2025-07-17 15:52:42,321 - main - INFO - 检查依赖和配置...
2025-07-17 15:52:42,323 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 15:52:42,324 - main - INFO - 依赖检查完成
2025-07-17 15:52:42,325 - main - INFO - ============================================================
2025-07-17 15:52:42,326 - main - INFO - 微信客服机器人状态
2025-07-17 15:52:42,327 - main - INFO - ============================================================
2025-07-17 15:52:42,328 - main - INFO - 监听列表: []
2025-07-17 15:52:42,329 - main - INFO - 自动回复: 启用
2025-07-17 15:52:42,330 - main - INFO - 回复延迟: 2秒
2025-07-17 15:52:42,331 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 15:52:42,331 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 15:52:42,332 - main - INFO - 相似度阈值: 0.7
2025-07-17 15:52:42,337 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 15:52:42,340 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 15:52:42,345 - main - INFO - API密钥: 已配置
2025-07-17 15:52:42,346 - main - INFO - ============================================================
2025-07-17 15:52:42,600 - main - INFO - 正在启动微信消息监听...
2025-07-17 15:52:48,143 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-17 15:52:48,145 - main - INFO - 💡 提示:
2025-07-17 15:52:48,146 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-17 15:52:48,149 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-17 15:52:48,151 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-17 15:54:09,060 - main - INFO - 收到退出信号，正在关闭程序...
2025-07-17 15:56:02,015 - main - INFO - 启动微信客服机器人...
2025-07-17 15:56:02,015 - main - INFO - 检查依赖和配置...
2025-07-17 15:56:02,016 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 15:56:02,017 - main - INFO - 依赖检查完成
2025-07-17 15:56:02,017 - main - INFO - ============================================================
2025-07-17 15:56:02,018 - main - INFO - 微信客服机器人状态
2025-07-17 15:56:02,018 - main - INFO - ============================================================
2025-07-17 15:56:02,019 - main - INFO - 监听列表: []
2025-07-17 15:56:02,020 - main - INFO - 自动回复: 启用
2025-07-17 15:56:02,020 - main - INFO - 回复延迟: 2秒
2025-07-17 15:56:02,021 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 15:56:02,021 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 15:56:02,022 - main - INFO - 相似度阈值: 0.7
2025-07-17 15:56:02,022 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 15:56:02,023 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 15:56:02,023 - main - INFO - API密钥: 已配置
2025-07-17 15:56:02,024 - main - INFO - ============================================================
2025-07-17 15:56:02,282 - main - INFO - 正在启动微信消息监听...
2025-07-17 15:56:07,922 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-17 15:56:07,922 - main - INFO - 💡 提示:
2025-07-17 15:56:07,923 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-17 15:56:07,924 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-17 15:56:07,924 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-17 15:58:38,645 - main - INFO - 启动微信客服机器人...
2025-07-17 15:58:38,646 - main - INFO - 检查依赖和配置...
2025-07-17 15:58:38,647 - main - WARNING - 监听列表为空，请在配置中添加要监听的聊天对象
2025-07-17 15:58:38,648 - main - INFO - 依赖检查完成
2025-07-17 15:58:38,649 - main - INFO - ============================================================
2025-07-17 15:58:38,650 - main - INFO - 微信客服机器人状态
2025-07-17 15:58:38,652 - main - INFO - ============================================================
2025-07-17 15:58:38,653 - main - INFO - 监听列表: []
2025-07-17 15:58:38,654 - main - INFO - 自动回复: 启用
2025-07-17 15:58:38,655 - main - INFO - 回复延迟: 2秒
2025-07-17 15:58:38,656 - main - INFO - FAQ文件: data/faq.xlsx
2025-07-17 15:58:38,657 - main - INFO - 产品文件: data/products.xlsx
2025-07-17 15:58:38,658 - main - INFO - 相似度阈值: 0.7
2025-07-17 15:58:38,662 - main - INFO - AI模型: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
2025-07-17 15:58:38,663 - main - INFO - API地址: https://api.siliconflow.cn/v1
2025-07-17 15:58:38,664 - main - INFO - API密钥: 已配置
2025-07-17 15:58:38,665 - main - INFO - ============================================================
2025-07-17 15:58:38,908 - main - INFO - 正在启动微信消息监听...
2025-07-17 15:58:44,447 - main - INFO - ✅ 微信客服机器人启动成功！
2025-07-17 15:58:44,449 - main - INFO - 💡 提示:
2025-07-17 15:58:44,451 - main - INFO -    - 按 Ctrl+C 退出程序
2025-07-17 15:58:44,452 - main - INFO -    - 运行 python web_config.py 打开配置界面
2025-07-17 15:58:44,453 - main - INFO -    - 确保微信PC版保持登录状态
2025-07-17 15:59:21,956 - main - INFO - 收到退出信号，正在关闭程序...
