"""
Web配置界面
提供简洁的配置管理界面
"""
import os
import sys
from pathlib import Path
from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash, send_file
from werkzeug.utils import secure_filename
import pandas as pd
import json

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
sys.path.insert(0, str(project_root))

from config import config
from src.utils.logger import get_logger

# 尝试导入增强回复引擎，如果失败则使用原版
try:
    from src.bot.enhanced_reply_engine import EnhancedReplyEngine as ReplyEngine
    from src.database.enhanced_reader import <PERSON>hanced<PERSON><PERSON><PERSON>eader as FAQReader, EnhancedProductReader as ProductReader
    ENHANCED_MODE = True
    logger = get_logger("web_app")
    logger.info("使用增强回复引擎和数据读取器")
except ImportError:
    from src.database.excel_reader import FAQReader, ProductReader
    from src.bot.reply_engine import ReplyEngine
    ENHANCED_MODE = False
    logger = get_logger("web_app")
    logger.info("使用标准回复引擎和数据读取器")

logger = get_logger("web_app")

app = Flask(__name__)
app.secret_key = config.web.secret_key

# 全局变量
reply_engine = None


def initialize():
    """初始化"""
    global reply_engine
    try:
        reply_engine = ReplyEngine()
        logger.info("Web应用初始化完成")
    except Exception as e:
        logger.error(f"Web应用初始化失败: {e}")

# 在应用启动时初始化
with app.app_context():
    initialize()


@app.route('/')
def index():
    """首页"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))


@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        password = request.form.get('password')
        if password == config.web.password:
            session['logged_in'] = True
            flash('登录成功', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('密码错误', 'error')
    
    return render_template('login.html')


@app.route('/logout')
def logout():
    """退出登录"""
    session.pop('logged_in', None)
    flash('已退出登录', 'info')
    return redirect(url_for('login'))


@app.route('/dashboard')
def dashboard():
    """仪表板"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    # 直接使用API统计信息方法，避免仪表板路由的问题
    try:
        # 检查AI服务可用性
        ai_available = False
        try:
            from src.ai.llm_service import LLMService

            llm_service = LLMService(
                api_key=config.ai.api_key,
                base_url=config.ai.base_url,
                model=config.ai.model,
                max_tokens=config.ai.max_tokens,
                temperature=config.ai.temperature,
                system_prompt=config.ai.system_prompt
            )
            ai_available = llm_service.is_available()
        except Exception as e:
            logger.debug(f"检查AI服务失败: {e}")
            ai_available = False

        # 获取基础数据统计
        faq_count = 0
        product_count = 0
        if reply_engine:
            try:
                if hasattr(reply_engine, 'faq_reader') and reply_engine.faq_reader and hasattr(reply_engine.faq_reader, 'data'):
                    if reply_engine.faq_reader.data is not None:
                        faq_count = len(reply_engine.faq_reader.data)

                if hasattr(reply_engine, 'product_reader') and reply_engine.product_reader and hasattr(reply_engine.product_reader, 'data'):
                    if reply_engine.product_reader.data is not None:
                        product_count = len(reply_engine.product_reader.data)
            except Exception as e:
                logger.debug(f"获取数据统计失败: {e}")

        # 构建统计信息
        stats = {
            'faq_count': faq_count,
            'product_count': product_count,
            'product_categories': [],
            'faq_categories': [],
            'ai_available': ai_available,
            'enhanced_mode': ENHANCED_MODE,
            'current_model': config.ai.model,
            'ai_config': {
                'model': config.ai.model,
                'base_url': config.ai.base_url,
                'enabled': config.ai.enabled
            },
            'status': 'normal'
        }

        return render_template('dashboard.html', stats=stats, config=config)
    except Exception as e:
        logger.error(f"加载仪表板失败: {e}")
        flash('加载仪表板失败', 'error')
        # 提供完整的默认统计信息
        default_stats = {
            'faq_count': 0,
            'product_count': 0,
            'product_categories': [],
            'faq_categories': [],
            'ai_available': False,
            'enhanced_mode': False,
            'current_model': config.ai.model,
            'status': 'error'
        }
        return render_template('dashboard.html', stats=default_stats, config=config)


@app.route('/config')
def config_page():
    """配置页面"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    try:
        logger.info(f"配置对象类型: {type(config)}")
        logger.info(f"系统配置: {config.wechat}")
        logger.info(f"系统配置类型: {type(config.wechat)}")
        logger.info(f"listen_all属性: {hasattr(config.wechat, 'listen_all')}")
        if hasattr(config.wechat, 'listen_all'):
            logger.info(f"listen_all值: {config.wechat.listen_all}")

        return render_template('config.html', config=config)
    except Exception as e:
        logger.error(f"配置页面错误: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return f"配置页面错误: {str(e)}", 500


@app.route('/test')
def test_route():
    """简单测试路由"""
    return "测试路由工作正常！"

@app.route('/config_test')
def config_test():
    """配置页面测试"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    try:
        return f"""
        <html>
        <head><title>配置测试</title></head>
        <body>
            <h1>配置测试页面</h1>
            <p>配置对象: {config}</p>
            <p>系统配置: {config.wechat}</p>
            <p>监听列表: {config.wechat.listen_list}</p>
            <p>监听所有: {config.wechat.listen_all}</p>
            <p>自动回复: {config.wechat.auto_reply}</p>
            <p>回复延迟: {config.wechat.reply_delay}</p>
        </body>
        </html>
        """
    except Exception as e:
        return f"配置测试页面错误: {str(e)}", 500


@app.route('/api/save_config', methods=['POST'])
def save_config():
    """保存配置"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401

    try:
        new_config = request.get_json()

        # 验证配置数据
        if not new_config:
            return jsonify({'error': '配置数据为空'}), 400

        # 读取当前配置文件
        config_path = project_root / 'config' / 'config.json'

        with open(config_path, 'r', encoding='utf-8') as f:
            current_config = json.load(f)

        # 更新配置
        if 'wechat' in new_config:
            current_config['wechat'].update(new_config['wechat'])
        if 'ai' in new_config:
            current_config['ai'].update(new_config['ai'])
        if 'database' in new_config:
            current_config['database'].update(new_config['database'])
        if 'reply' in new_config:
            current_config['reply'].update(new_config['reply'])

        # 保存配置文件
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(current_config, f, ensure_ascii=False, indent=2)

        # 重新加载配置
        global config
        from config import config as new_config_obj
        config = new_config_obj

        logger.info("配置已保存并重新加载")
        return jsonify({'message': '配置保存成功'})

    except Exception as e:
        logger.error(f"保存配置失败: {e}")
        return jsonify({'error': f'保存配置失败: {str(e)}'}), 500


@app.route('/api/config', methods=['GET', 'POST'])
def api_config():
    """配置API"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401
    
    if request.method == 'GET':
        # 返回当前配置
        return jsonify({
            'wechat': {
                'listen_list': config.wechat.listen_list,
                'auto_reply': config.wechat.auto_reply,
                'reply_delay': config.wechat.reply_delay
            },
            'ai': {
                'api_key': config.ai.api_key,
                'base_url': config.ai.base_url,
                'model': config.ai.model,
                'max_tokens': config.ai.max_tokens,
                'temperature': config.ai.temperature,
                'enabled': config.ai.enabled,
                'system_prompt': config.ai.system_prompt
            },
            'database': {
                'faq_file': config.database.faq_file,
                'products_file': config.database.products_file,
                'similarity_threshold': config.database.similarity_threshold
            },
            'reply': {
                'priority_faq': config.reply.priority_faq,
                'priority_products': config.reply.priority_products,
                'use_ai_fallback': config.reply.use_ai_fallback,
                'default_reply': config.reply.default_reply
            }
        })
    
    elif request.method == 'POST':
        try:
            data = request.json
            
            # 更新配置
            if 'wechat' in data:
                wechat_data = data['wechat']
                config.wechat.listen_list = wechat_data.get('listen_list', [])
                config.wechat.auto_reply = wechat_data.get('auto_reply', True)
                config.wechat.reply_delay = wechat_data.get('reply_delay', 2)
            
            if 'ai' in data:
                ai_data = data['ai']
                config.ai.api_key = ai_data.get('api_key', '')
                config.ai.base_url = ai_data.get('base_url', '')
                config.ai.model = ai_data.get('model', '')
                config.ai.max_tokens = ai_data.get('max_tokens', 1000)
                config.ai.temperature = ai_data.get('temperature', 0.7)
                config.ai.enabled = ai_data.get('enabled', True)
                config.ai.system_prompt = ai_data.get('system_prompt', config.ai.system_prompt)
            
            if 'database' in data:
                db_data = data['database']
                config.database.faq_file = db_data.get('faq_file', '')
                config.database.products_file = db_data.get('products_file', '')
                config.database.similarity_threshold = db_data.get('similarity_threshold', 0.7)
            
            if 'reply' in data:
                reply_data = data['reply']
                config.reply.priority_faq = reply_data.get('priority_faq', True)
                config.reply.priority_products = reply_data.get('priority_products', True)
                config.reply.use_ai_fallback = reply_data.get('use_ai_fallback', True)
                config.reply.default_reply = reply_data.get('default_reply', '')
            
            # 保存配置
            config.save_config()
            
            # 重新加载数据
            if reply_engine:
                reply_engine.reload_data()
            
            return jsonify({'success': True, 'message': '配置保存成功'})
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            return jsonify({'error': f'保存配置失败: {str(e)}'}), 500


@app.route('/faq')
def faq_page():
    """FAQ管理页面"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    try:
        faq_list = []

        # 使用全局的增强回复引擎中的FAQ读取器
        if reply_engine and hasattr(reply_engine, 'faq_reader') and reply_engine.faq_reader:
            faq_reader = reply_engine.faq_reader
            if hasattr(faq_reader, 'data') and faq_reader.data is not None and not faq_reader.data.empty:
                faq_list = faq_reader.data.to_dict('records')

        return render_template('faq.html', faq_list=faq_list)
    except Exception as e:
        logger.error(f"加载FAQ页面失败: {e}")
        flash('加载FAQ数据失败', 'error')
        return render_template('faq.html', faq_list=[])


@app.route('/products')
def products_page():
    """产品管理页面"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    try:
        product_list = []

        # 使用全局的增强回复引擎中的产品读取器
        if reply_engine and hasattr(reply_engine, 'product_reader') and reply_engine.product_reader:
            product_reader = reply_engine.product_reader
            if hasattr(product_reader, 'data') and product_reader.data is not None and not product_reader.data.empty:
                product_list = product_reader.data.to_dict('records')

        return render_template('products.html', product_list=product_list)
    except Exception as e:
        logger.error(f"加载产品页面失败: {e}")
        flash('加载产品数据失败', 'error')
        return render_template('products.html', product_list=[])


@app.route('/api/reload_data', methods=['POST'])
def reload_data():
    """重新加载数据"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401
    
    try:
        if reply_engine:
            reply_engine.reload_data()
        return jsonify({'success': True, 'message': '数据重新加载成功'})
    except Exception as e:
        logger.error(f"重新加载数据失败: {e}")
        return jsonify({'error': f'重新加载数据失败: {str(e)}'}), 500


@app.route('/api/test_ai', methods=['POST'])
def test_ai():
    """测试AI连接"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401

    try:
        # 直接创建LLM服务进行测试
        from src.ai.llm_service import LLMService

        llm_service = LLMService(
            api_key=config.ai.api_key,
            base_url=config.ai.base_url,
            model=config.ai.model,
            max_tokens=config.ai.max_tokens,
            temperature=config.ai.temperature,
            system_prompt=config.ai.system_prompt
        )

        if llm_service.is_available():
            # 进行实际的连接测试
            test_result = llm_service.test_connection()
            if test_result:
                return jsonify({'success': True, 'message': 'AI连接测试成功'})
            else:
                return jsonify({'error': 'AI连接测试失败'})
        else:
            return jsonify({'error': 'AI服务不可用'})

    except Exception as e:
        logger.error(f"AI连接测试失败: {e}")
        return jsonify({'error': f'AI连接测试失败: {str(e)}'}), 500


@app.route('/api/available_models')
def get_available_models():
    """获取可用的AI模型列表"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401

    try:
        import requests

        headers = {
            "Authorization": f"Bearer {config.ai.api_key}",
            "Content-Type": "application/json"
        }

        # 获取模型列表
        models_url = "https://api.siliconflow.cn/v1/models"
        response = requests.get(models_url, headers=headers, timeout=10)

        if response.status_code == 200:
            models_data = response.json()
            if 'data' in models_data:
                # 过滤聊天模型
                chat_models = []
                for model in models_data['data']:
                    model_id = model.get('id', '')
                    # 过滤聊天和指令模型
                    if any(keyword in model_id.lower() for keyword in ['chat', 'instruct', 'deepseek', 'qwen', 'glm']):
                        chat_models.append({
                            'id': model_id,
                            'name': model_id,
                            'description': f"模型: {model_id}",
                            'current': model_id == config.ai.model
                        })

                # 按名称排序
                chat_models.sort(key=lambda x: x['id'])

                return jsonify({
                    'success': True,
                    'models': chat_models,
                    'current_model': config.ai.model,
                    'total_count': len(chat_models)
                })
            else:
                return jsonify({'error': '模型列表格式异常'}), 500
        else:
            return jsonify({'error': f'获取模型列表失败: {response.status_code}'}), 500

    except Exception as e:
        logger.error(f"获取可用模型失败: {e}")
        return jsonify({'error': f'获取可用模型失败: {str(e)}'}), 500


@app.route('/api/switch_model', methods=['POST'])
def switch_model():
    """切换AI模型"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401

    try:
        data = request.get_json()
        new_model = data.get('model')

        if not new_model:
            return jsonify({'error': '模型名称不能为空'}), 400

        # 读取当前配置
        config_path = project_root / 'config' / 'config.json'
        with open(config_path, 'r', encoding='utf-8') as f:
            current_config = json.load(f)

        # 更新模型配置
        old_model = current_config['ai']['model']
        current_config['ai']['model'] = new_model

        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(current_config, f, ensure_ascii=False, indent=2)

        # 重新加载配置
        global config
        from config import config as new_config_obj
        config = new_config_obj

        logger.info(f"AI模型已切换: {old_model} -> {new_model}")

        return jsonify({
            'success': True,
            'message': f'模型已切换为: {new_model}',
            'old_model': old_model,
            'new_model': new_model
        })

    except Exception as e:
        logger.error(f"切换模型失败: {e}")
        return jsonify({'error': f'切换模型失败: {str(e)}'}), 500


@app.route('/api/stats')
def api_stats():
    """获取统计信息"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401

    try:
        if reply_engine:
            # 获取基础统计信息
            stats = reply_engine.get_statistics()

            # 确保包含所有必要字段
            if 'ai_available' not in stats:
                # 如果统计信息中没有ai_available，尝试检查AI服务
                try:
                    from src.ai.llm_service import LLMService

                    llm_service = LLMService(
                        api_key=config.ai.api_key,
                        base_url=config.ai.base_url,
                        model=config.ai.model,
                        max_tokens=config.ai.max_tokens,
                        temperature=config.ai.temperature,
                        system_prompt=config.ai.system_prompt
                    )
                    stats['ai_available'] = llm_service.is_available()
                except Exception as e:
                    logger.debug(f"检查AI服务失败: {e}")
                    stats['ai_available'] = False

            # 确保包含分类信息
            if 'faq_categories' not in stats:
                stats['faq_categories'] = []
            if 'product_categories' not in stats:
                stats['product_categories'] = []

            # 添加模式信息和AI配置信息
            stats['enhanced_mode'] = ENHANCED_MODE
            stats['current_model'] = config.ai.model
            stats['ai_config'] = {
                'model': config.ai.model,
                'base_url': config.ai.base_url,
                'enabled': config.ai.enabled
            }

        else:
            # 没有回复引擎时的默认统计信息
            stats = {
                'faq_count': 0,
                'product_count': 0,
                'product_categories': [],
                'faq_categories': [],
                'ai_available': False,
                'enhanced_mode': False,
                'status': 'no_engine'
            }

        return jsonify(stats)
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({
            'faq_count': 0,
            'product_count': 0,
            'product_categories': [],
            'faq_categories': [],
            'ai_available': False,
            'enhanced_mode': False,
            'status': 'error',
            'error': str(e)
        }), 500


@app.route('/api/reload_data', methods=['POST'])
def api_reload_data():
    """重新加载数据"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401

    try:
        global reply_engine
        if reply_engine and hasattr(reply_engine, 'reload_data'):
            reply_engine.reload_data()
            return jsonify({'message': '数据重新加载成功'})
        else:
            # 重新初始化回复引擎
            reply_engine = ReplyEngine()
            return jsonify({'message': '回复引擎重新初始化成功'})
    except Exception as e:
        logger.error(f"重新加载数据失败: {e}")
        return jsonify({'error': f'重新加载数据失败: {str(e)}'}), 500


@app.route('/api/test_reply', methods=['POST'])
def api_test_reply():
    """测试回复功能"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401

    try:
        data = request.get_json()
        message = data.get('message', '')

        if not message:
            return jsonify({'error': '消息不能为空'}), 400

        if reply_engine:
            reply = reply_engine.generate_reply(message)
            return jsonify({
                'message': message,
                'reply': reply,
                'enhanced_mode': ENHANCED_MODE
            })
        else:
            return jsonify({'error': '回复引擎未初始化'}), 500

    except Exception as e:
        logger.error(f"测试回复失败: {e}")
        return jsonify({'error': f'测试回复失败: {str(e)}'}), 500


@app.route('/data_management')
def data_management():
    """数据管理页面"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    return render_template('data_management.html')


@app.route('/api/upload_faq', methods=['POST'])
def upload_faq():
    """上传FAQ数据"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401

    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        replace = request.form.get('replace', 'false').lower() == 'true'

        # 保存上传的文件
        filename = secure_filename(file.filename)
        upload_path = os.path.join(project_root, 'temp', filename)
        os.makedirs(os.path.dirname(upload_path), exist_ok=True)
        file.save(upload_path)

        # 使用数据管理器处理
        sys.path.append(str(project_root))
        from data_manager import DataManager

        manager = DataManager()
        success = manager.upload_faq_data(upload_path, replace)

        # 清理临时文件
        os.remove(upload_path)

        if success:
            return jsonify({'message': 'FAQ数据上传成功'})
        else:
            return jsonify({'error': 'FAQ数据上传失败'}), 500

    except Exception as e:
        logger.error(f"上传FAQ数据失败: {e}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500


@app.route('/api/upload_products', methods=['POST'])
def upload_products():
    """上传产品数据"""
    if not session.get('logged_in'):
        return jsonify({'error': '未登录'}), 401

    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        replace = request.form.get('replace', 'false').lower() == 'true'

        # 保存上传的文件
        filename = secure_filename(file.filename)
        upload_path = os.path.join(project_root, 'temp', filename)
        os.makedirs(os.path.dirname(upload_path), exist_ok=True)
        file.save(upload_path)

        # 使用数据管理器处理
        sys.path.append(str(project_root))
        from data_manager import DataManager

        manager = DataManager()
        success = manager.upload_products_data(upload_path, replace)

        # 清理临时文件
        os.remove(upload_path)

        if success:
            return jsonify({'message': '产品数据上传成功'})
        else:
            return jsonify({'error': '产品数据上传失败'}), 500

    except Exception as e:
        logger.error(f"上传产品数据失败: {e}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500


@app.route('/api/download_template/<data_type>')
def download_template(data_type):
    """下载数据模板"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    try:
        sys.path.append(str(project_root))
        from data_manager import DataManager

        manager = DataManager()
        template_path = manager.export_template(data_type)

        if template_path and os.path.exists(template_path):
            return send_file(template_path, as_attachment=True)
        else:
            flash('模板生成失败', 'error')
            return redirect(url_for('data_management'))

    except Exception as e:
        logger.error(f"下载模板失败: {e}")
        flash(f'下载模板失败: {str(e)}', 'error')
        return redirect(url_for('data_management'))


if __name__ == '__main__':
    app.run(
        host=config.web.host,
        port=config.web.port,
        debug=True  # 临时启用调试模式
    )
