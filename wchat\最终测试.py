#!/usr/bin/env python3
"""
最终测试脚本 - 使用稳定版本wxauto
"""
import time

def test_stable_wxauto():
    """测试稳定版本wxauto"""
    print("=" * 50)
    print("测试稳定版本wxauto (3.9.8.15.7)")
    print("=" * 50)
    
    try:
        from wxauto import WeChat
        print("正在连接微信...")
        
        # 创建微信对象
        wx = WeChat()
        
        # 等待更长时间
        print("等待连接稳定（10秒）...")
        time.sleep(10)
        
        # 测试连接
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"✅ 连接成功！用户: {wx.nickname}")
            
            # 测试基本功能
            try:
                current_chat = wx.CurrentChat()
                print(f"当前聊天: {current_chat}")
            except Exception as e:
                print(f"CurrentChat测试: {e}")
            
            return True
        else:
            print("❌ 无法获取用户信息")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        
        # 错误分析
        error_str = str(e)
        if "SetWindowPos" in error_str:
            print("\n🔧 仍然是窗口句柄错误，请尝试:")
            print("1. 将显示缩放设置为100%")
            print("2. 以管理员身份运行")
            print("3. 重启电脑")
        elif "找不到" in error_str or "not found" in error_str:
            print("\n🔧 微信窗口未找到，请确保:")
            print("1. 微信PC版已启动并登录")
            print("2. 微信窗口可见（不要最小化到托盘）")
        
        return False

def main():
    """主函数"""
    print("最终测试 - 请确保:")
    print("1. 微信PC版已启动并完全登录")
    print("2. 微信窗口可见")
    print("3. 显示缩放为100%")
    print("4. 以管理员身份运行")
    print()
    
    if test_stable_wxauto():
        print("\n🎉 测试成功！现在可以启动WChat机器人了")
        print("运行: python run.py")
    else:
        print("\n❌ 测试失败")
        print("\n最后的建议:")
        print("1. 重启电脑")
        print("2. 确保显示缩放为100%")
        print("3. 以管理员身份运行所有程序")
        print("4. 如果还不行，可能需要使用其他微信自动化方案")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
