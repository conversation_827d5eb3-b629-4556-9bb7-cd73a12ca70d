# 产品管理功能说明

## 🎯 功能概述

微信客服机器人现已支持完整的产品管理功能，包括：

### ✅ 核心功能
1. **关键词智能匹配** - 支持产品名称、关键词、描述的模糊匹配
2. **产品图片支持** - 每个产品可配置图片路径
3. **详细产品信息** - 支持价格、规格、库存等详细信息
4. **数据上传替换** - 支持Excel/CSV文件上传和数据替换
5. **Web管理界面** - 可视化的数据管理界面
6. **模板下载** - 提供标准的数据模板

## 📊 数据结构

### FAQ数据结构
| 字段名 | 说明 | 必填 | 示例 |
|--------|------|------|------|
| 问题关键词 | 用逗号分隔的关键词 | ✅ | "退货,退款,退换" |
| 标准问题 | 标准化的问题描述 | ✅ | "如何申请退货退款？" |
| 回复内容 | 回复的答案内容 | ✅ | "您可以在订单页面..." |
| 分类 | 问题分类 | ✅ | "售后服务" |
| 状态 | 启用/禁用 | ✅ | "启用" |

### 产品数据结构
| 字段名 | 说明 | 必填 | 示例 |
|--------|------|------|------|
| 产品名称 | 产品的名称 | ✅ | "智能手机A1" |
| 产品关键词 | 用逗号分隔的关键词 | ✅ | "手机,智能手机,A1" |
| 产品描述 | 产品的详细描述 | ✅ | "6.1寸全面屏，大电池" |
| 价格 | 产品价格（数字） | ✅ | 2999.0 |
| 分类 | 产品分类 | ✅ | "数码产品" |
| 产品图片 | 图片文件路径 | ❌ | "images/phone_a1.jpg" |
| 详细信息 | 详细的产品信息 | ❌ | "屏幕：6.1寸\\n电池：5000mAh" |
| 库存状态 | 有货/缺货 | ❌ | "有货" |
| 状态 | 上架/下架 | ❌ | "上架" |

## 🔍 智能匹配机制

### 关键词匹配
- **精确匹配**：关键词完全包含在用户消息中
- **模糊匹配**：使用模糊字符串匹配算法
- **权重评分**：不同字段有不同的匹配权重

### 匹配优先级
1. **产品名称匹配** (权重: 1.5)
2. **关键词匹配** (权重: 1.0)
3. **描述匹配** (权重: 0.8)
4. **分类匹配** (权重: 0.6)

### 回复格式
```
为您找到 X 款相关产品：

🛍️ 1. 产品名称
💰 价格：¥XXXX
📝 描述：产品描述
📦 库存：库存状态
ℹ️ 详情：详细信息

如需了解更多信息或购买，请联系客服！
```

## 🛠️ 使用方法

### 方法1：Web管理界面（推荐）

1. **访问管理界面**
   ```
   http://localhost:5000/data_management
   ```

2. **上传数据**
   - 选择FAQ或产品数据文件
   - 选择是否替换现有数据
   - 点击上传按钮

3. **下载模板**
   - 点击"下载模板"按钮
   - 获取标准格式的Excel模板

### 方法2：命令行工具

```bash
# 启动数据管理工具
python wchat\data_manager.py

# 选择相应的功能：
# 1. 上传FAQ数据
# 2. 上传产品数据
# 3. 上传产品图片
# 4. 导出数据模板
# 5. 查看备份列表
# 6. 恢复备份
```

### 方法3：Python API

```python
from wchat.data_manager import DataManager

# 创建数据管理器
manager = DataManager()

# 上传FAQ数据
manager.upload_faq_data('path/to/faq.xlsx', replace=True)

# 上传产品数据
manager.upload_products_data('path/to/products.xlsx', replace=False)

# 导出模板
manager.export_template('faq')
manager.export_template('products')
```

## 📁 文件结构

```
wchat/
├── data/
│   ├── faq_enhanced.xlsx          # FAQ数据库
│   ├── products_enhanced.xlsx     # 产品数据库
│   ├── images/                    # 产品图片文件夹
│   │   ├── phone_a1.jpg
│   │   ├── earphone_b2.jpg
│   │   └── ...
│   ├── backups/                   # 数据备份文件夹
│   ├── faq_template.xlsx          # FAQ模板
│   └── products_template.xlsx     # 产品模板
├── src/
│   ├── database/
│   │   └── enhanced_reader.py     # 增强数据读取器
│   ├── bot/
│   │   └── enhanced_reply_engine.py # 增强回复引擎
│   └── web/
│       ├── app.py                 # Web应用
│       └── templates/
│           └── data_management.html # 数据管理页面
└── data_manager.py               # 数据管理工具
```

## 🧪 测试示例

### FAQ测试
```
用户: "如何退货"
机器人: "您可以在订单页面点击'申请退货'，或联系客服办理退货退款..."

用户: "什么时候发货"
机器人: "我们会在您付款后24小时内安排发货，节假日可能会有延迟..."
```

### 产品查询测试
```
用户: "推荐一款手机"
机器人: "为您找到 1 款相关产品：
🛍️ 1. 智能手机A1
💰 价格：¥2999
📝 描述：6.1寸全面屏，128GB存储，5000mAh大电池..."

用户: "充电器多少钱"
机器人: "为您找到 1 款相关产品：
🛍️ 1. 无线充电器E5
💰 价格：¥99
📝 描述：15W快速无线充电，支持多种设备..."
```

## 🔄 数据备份与恢复

### 自动备份
- 每次替换数据时自动创建备份
- 备份文件命名格式：`数据类型_backup_时间戳.xlsx`
- 备份保存在 `data/backups/` 目录

### 手动恢复
1. 使用Web界面或命令行工具
2. 选择要恢复的备份文件
3. 确认恢复操作

## 📈 统计信息

系统提供以下统计信息：
- FAQ数据条数
- 产品数据条数
- 产品分类数量
- 数据状态（正常/异常）

## 🚀 启动增强机器人

### 使用增强回复引擎

```python
# 在 run_simple.py 中替换回复引擎
from src.bot.enhanced_reply_engine import EnhancedReplyEngine

# 创建增强回复引擎
engine = EnhancedReplyEngine()

# 生成回复
reply = engine.generate_reply(user_message)
```

### 完整启动流程

1. **准备数据**
   ```bash
   # 创建增强数据结构
   python wchat\create_enhanced_data.py
   ```

2. **启动机器人**
   ```bash
   # 使用简化版机器人（已集成增强功能）
   python wchat\run_simple.py
   ```

3. **管理数据**
   ```bash
   # 访问Web管理界面
   http://localhost:5000/data_management
   ```

## 🎯 最佳实践

### 数据准备
1. **关键词设置**：为每个产品设置多个相关关键词
2. **描述优化**：产品描述要简洁明了，突出卖点
3. **分类管理**：合理设置产品分类，便于管理
4. **图片命名**：使用有意义的文件名，便于识别

### 性能优化
1. **定期清理**：删除无用的备份文件
2. **数据压缩**：大量数据时考虑分批上传
3. **缓存更新**：数据更新后重启机器人以刷新缓存

### 安全建议
1. **备份重要**：重要数据更新前先备份
2. **权限控制**：Web界面设置访问权限
3. **数据验证**：上传前检查数据格式和内容

## 🔧 故障排除

### 常见问题

**Q: 上传数据后机器人没有更新？**
A: 需要重启机器人以重新加载数据

**Q: 产品匹配不准确？**
A: 检查关键词设置，增加更多相关关键词

**Q: 图片无法显示？**
A: 确保图片文件存在于 `data/images/` 目录中

**Q: 数据格式错误？**
A: 下载模板文件，按照标准格式填写数据

## 📞 技术支持

如需技术支持，请：
1. 查看日志文件了解详细错误信息
2. 检查数据格式是否符合要求
3. 确认所有依赖包已正确安装
4. 联系技术支持团队

---

**🎉 现在您的微信客服机器人已具备完整的产品管理功能！**
