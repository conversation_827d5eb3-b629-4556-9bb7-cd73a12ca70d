#!/usr/bin/env python3
"""
安全的微信消息发送工具
解决发送消息时识别错误控件的问题
"""
import time
import win32gui
import win32con
from typing import Optional

def safe_send_message(wx_instance, message: str, chat_name: str = None) -> bool:
    """
    安全发送消息，避免识别错误控件
    
    Args:
        wx_instance: 微信实例对象
        message: 要发送的消息
        chat_name: 聊天对象名称，None表示当前聊天
        
    Returns:
        bool: 发送是否成功
    """
    if not wx_instance or not message:
        return False
    
    try:
        # 方法1: 使用改进的发送逻辑
        if chat_name:
            # 切换到指定聊天
            if not safe_chat_with(wx_instance, chat_name):
                print(f"无法切换到聊天: {chat_name}")
                return False
        
        # 等待界面稳定
        time.sleep(0.5)
        
        # 尝试多种发送方法
        success = False
        
        # 方法1: 标准SendMsg
        try:
            wx_instance.SendMsg(msg=message)
            success = True
            print(f"方法1发送成功: {message[:30]}...")
        except Exception as e:
            print(f"方法1失败: {e}")
        
        # 方法2: 如果方法1失败，尝试带参数的SendMsg
        if not success:
            try:
                if chat_name:
                    wx_instance.SendMsg(msg=message, who=chat_name)
                else:
                    wx_instance.SendMsg(msg=message, who=None)
                success = True
                print(f"方法2发送成功: {message[:30]}...")
            except Exception as e:
                print(f"方法2失败: {e}")
        
        # 方法3: 如果还是失败，尝试模拟键盘输入
        if not success:
            try:
                success = send_by_keyboard_simulation(wx_instance, message)
                if success:
                    print(f"方法3发送成功: {message[:30]}...")
            except Exception as e:
                print(f"方法3失败: {e}")
        
        return success
        
    except Exception as e:
        print(f"发送消息异常: {e}")
        return False

def safe_chat_with(wx_instance, chat_name: str) -> bool:
    """
    安全切换聊天窗口
    
    Args:
        wx_instance: 微信实例对象
        chat_name: 聊天对象名称
        
    Returns:
        bool: 切换是否成功
    """
    try:
        # 尝试多次切换，因为有时候第一次会失败
        for attempt in range(3):
            try:
                result = wx_instance.ChatWith(chat_name)
                if result:
                    time.sleep(0.5)  # 等待界面切换完成
                    return True
            except Exception as e:
                print(f"切换聊天尝试 {attempt + 1} 失败: {e}")
                time.sleep(1)
        
        return False
        
    except Exception as e:
        print(f"切换聊天异常: {e}")
        return False

def send_by_keyboard_simulation(wx_instance, message: str) -> bool:
    """
    通过键盘模拟发送消息
    
    Args:
        wx_instance: 微信实例对象
        message: 要发送的消息
        
    Returns:
        bool: 发送是否成功
    """
    try:
        import pyautogui
        import pyperclip
        
        # 获取微信窗口句柄
        hwnd = getattr(wx_instance, 'HWND', None)
        if not hwnd:
            return False
        
        # 激活微信窗口
        win32gui.SetForegroundWindow(hwnd)
        time.sleep(0.5)
        
        # 点击输入框区域（相对于微信窗口的位置）
        rect = win32gui.GetWindowRect(hwnd)
        window_width = rect[2] - rect[0]
        window_height = rect[3] - rect[1]
        
        # 输入框通常在窗口底部
        input_x = rect[0] + window_width // 2
        input_y = rect[3] - 50  # 距离底部50像素
        
        pyautogui.click(input_x, input_y)
        time.sleep(0.3)
        
        # 使用剪贴板发送消息（避免中文输入问题）
        pyperclip.copy(message)
        pyautogui.hotkey('ctrl', 'v')
        time.sleep(0.3)
        
        # 发送消息
        pyautogui.press('enter')
        time.sleep(0.3)
        
        return True
        
    except Exception as e:
        print(f"键盘模拟发送失败: {e}")
        return False

def find_input_box_position(wx_instance) -> Optional[tuple]:
    """
    查找输入框的准确位置
    
    Args:
        wx_instance: 微信实例对象
        
    Returns:
        Optional[tuple]: 输入框位置 (x, y)，失败返回None
    """
    try:
        import win32gui
        import win32con
        
        hwnd = getattr(wx_instance, 'HWND', None)
        if not hwnd:
            return None
        
        # 枚举微信窗口的子控件
        def enum_child_windows(hwnd, results):
            def callback(child_hwnd, param):
                class_name = win32gui.GetClassName(child_hwnd)
                window_text = win32gui.GetWindowText(child_hwnd)
                
                # 查找输入框控件
                if ('Edit' in class_name or 'RichEdit' in class_name) and \
                   win32gui.IsWindowVisible(child_hwnd):
                    rect = win32gui.GetWindowRect(child_hwnd)
                    # 输入框通常在窗口下方
                    if rect[1] > param['min_y']:
                        param['input_boxes'].append({
                            'hwnd': child_hwnd,
                            'rect': rect,
                            'class': class_name,
                            'text': window_text
                        })
                return True
            
            window_rect = win32gui.GetWindowRect(hwnd)
            min_y = window_rect[1] + (window_rect[3] - window_rect[1]) * 0.7  # 下方30%区域
            
            param = {'input_boxes': [], 'min_y': min_y}
            win32gui.EnumChildWindows(hwnd, callback, param)
            
            return param['input_boxes']
        
        input_boxes = enum_child_windows(hwnd, [])
        
        if input_boxes:
            # 选择最下方的输入框
            bottom_box = max(input_boxes, key=lambda x: x['rect'][1])
            rect = bottom_box['rect']
            center_x = (rect[0] + rect[2]) // 2
            center_y = (rect[1] + rect[3]) // 2
            return (center_x, center_y)
        
        return None
        
    except Exception as e:
        print(f"查找输入框位置失败: {e}")
        return None

def enhanced_send_message(wx_instance, message: str, chat_name: str = None) -> bool:
    """
    增强的消息发送方法，结合多种技术
    
    Args:
        wx_instance: 微信实例对象
        message: 要发送的消息
        chat_name: 聊天对象名称
        
    Returns:
        bool: 发送是否成功
    """
    print(f"开始发送消息: {message[:30]}...")
    
    # 步骤1: 切换聊天窗口
    if chat_name:
        if not safe_chat_with(wx_instance, chat_name):
            print(f"无法切换到聊天: {chat_name}")
            # 即使切换失败，也尝试发送（可能已经在正确的聊天中）
    
    # 步骤2: 尝试发送消息
    return safe_send_message(wx_instance, message, chat_name)

def install_pyautogui_if_needed():
    """安装pyautogui库（如果需要）"""
    try:
        import pyautogui
        import pyperclip
        return True
    except ImportError:
        print("正在安装pyautogui和pyperclip...")
        try:
            import subprocess
            import sys
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyautogui", "pyperclip"])
            print("pyautogui和pyperclip安装成功")
            return True
        except Exception as e:
            print(f"安装pyautogui失败: {e}")
            return False
