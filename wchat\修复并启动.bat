@echo off
chcp 65001 >nul
echo ========================================
echo     WChat微信连接修复和启动程序
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境！
    echo 💡 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 🔍 检查依赖包...
cd wchat
python -c "import wxauto" >nul 2>&1
if errorlevel 1 (
    echo ❌ wxauto库未安装，正在安装...
    python -m pip install wxauto --no-cache-dir
    if errorlevel 1 (
        echo ❌ wxauto安装失败！
        echo 💡 请手动安装: pip install wxauto
        pause
        exit /b 1
    )
)

echo ✅ 依赖包检查通过

echo.
echo 🔧 运行微信连接修复...
python 修复微信连接.py

echo.
echo 🚀 启动WChat机器人...
python 安全启动.py

echo.
echo 程序已退出
pause
