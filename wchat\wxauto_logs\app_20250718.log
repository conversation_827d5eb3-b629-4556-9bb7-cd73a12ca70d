2025-07-18 09:41:34 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 09:41:34 [wechat_handler] [DEBUG] [wechat_handler.py:145]  等待微信对象初始化...
2025-07-18 09:41:39 [wechat_handler] [DEBUG] [wechat_handler.py:205]  微信对象基本验证通过
2025-07-18 09:41:39 [wechat_handler] [INFO] [wechat_handler.py:158]  微信连接成功，当前用户: Rocky
2025-07-18 09:41:39 [wechat_handler] [DEBUG] [wechat_handler.py:159]  微信对象验证通过
2025-07-18 09:41:39 [wechat_handler] [INFO] [wechat_handler.py:314]  开始消息监听循环... [全局监听模式]
2025-07-18 09:41:39 [wechat_handler] [INFO] [wechat_handler.py:292]  开始监听微信消息
2025-07-18 09:41:39 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-18 09:41:39 [main] [INFO] [run.py:150]  💡 提示:
2025-07-18 09:41:39 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-18 09:41:39 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-18 09:41:39 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-18 09:41:40 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-18 09:41:43 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-18 09:41:43 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:41:43 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:41:43 [wxauto] [DEBUG] [msg.py:80]  content: 你好呀！很高兴见到你～有什么需要帮忙的吗？我这里有很多好东西哦，欢迎随时找我聊聊 😊, length: 8
2025-07-18 09:41:43 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:41:43 [wxauto] [DEBUG] [msg.py:80]  content: 嗨！有什么想了解的吗？我在这儿呢，随时帮你解答！, length: 8
2025-07-18 09:41:43 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:41:43 [wxauto] [DEBUG] [msg.py:80]  content: 你好呀！有什么需要帮忙的吗？交个朋友，我来帮你选选适合的产品！, length: 8
2025-07-18 09:41:43 [wechat_handler] [DEBUG] [wechat_handler.py:389]  跳过消息: system - 以下为新消息... (ID: e85bce83)
2025-07-18 09:41:43 [wechat_handler] [DEBUG] [wechat_handler.py:425]  全局监听模式，处理消息: 樂
2025-07-18 09:41:43 [wechat_handler] [INFO] [wechat_handler.py:384]  处理消息: 樂 - 你好... (ID: 23ecff62)
2025-07-18 09:41:43 [wechat_handler] [DEBUG] [wechat_handler.py:454]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:41:43 [wechat_handler] [DEBUG] [wechat_handler.py:510]  消息解析结果 - 发送者: 樂, 内容: 你好..., 类型: text
2025-07-18 09:41:43 [wechat_handler] [INFO] [wechat_handler.py:542]  消息详细信息 - 类型: text, 内容: 你好, 发送者: 樂
2025-07-18 09:41:43 [wechat_handler] [INFO] [wechat_handler.py:543]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:41:43 [wechat_handler] [INFO] [wechat_handler.py:625]  收到消息 - 发送者: 樂, 内容: 你好...
2025-07-18 09:41:43 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: 你好
2025-07-18 09:41:46 [llm_service] [INFO] [llm_service.py:36]  LLM客户端初始化成功
2025-07-18 09:41:46 [openai._base_client] [DEBUG] [_base_client.py:482]  Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-1c81fe1c-cf78-4993-9038-48f63d6227c9', 'json_data': {'messages': [{'role': 'system', 'content': '你是一个热情友好的销售顾问，对各类产品都很了解。\n\n你的特点：\n- 说话自然随和，就像朋友聊天一样\n- 对产品很熟悉，能给出实用的建议\n- 会根据用户需求推荐合适的产品\n- 不会说自己是AI或机器人\n- 不会提到联系客服或人工服务\n\n回复风格：\n- 用口语化的表达，比如这款挺不错的、我觉得、建议你看看\n- 简洁明了，一般20到50字就够了\n- 语气轻松友好，避免过于正式\n- 如果不确定，可以说我了解的不多或这个我不太清楚\n- 重点突出产品的实用价值和使用体验\n\n记住：你就是一个懂产品的朋友，自然地和用户交流就好。'}, {'role': 'user', 'content': '你好'}], 'model': 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B', 'max_tokens': 50, 'temperature': 0.7}}
2025-07-18 09:41:46 [openai._base_client] [DEBUG] [_base_client.py:968]  Sending HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions
2025-07-18 09:41:46 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.started host='127.0.0.1' port=8081 local_address=None timeout=5.0 socket_options=None
2025-07-18 09:41:46 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000001957064BAD0>
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'CONNECT']>
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-18 09:41:46 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.started ssl_context=<ssl.SSLContext object at 0x000001956F1E92E0> server_hostname='api.siliconflow.cn' timeout=5.0
2025-07-18 09:41:46 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000195706518D0>
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'POST']>
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'POST']>
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:41:46 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'POST']>
2025-07-18 09:42:05 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-18 09:42:13 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 09:42:13 [wechat_handler] [DEBUG] [wechat_handler.py:145]  等待微信对象初始化...
2025-07-18 09:42:18 [wechat_handler] [DEBUG] [wechat_handler.py:205]  微信对象基本验证通过
2025-07-18 09:42:18 [wechat_handler] [INFO] [wechat_handler.py:158]  微信连接成功，当前用户: Rocky
2025-07-18 09:42:18 [wechat_handler] [DEBUG] [wechat_handler.py:159]  微信对象验证通过
2025-07-18 09:42:18 [wechat_handler] [INFO] [wechat_handler.py:314]  开始消息监听循环... [全局监听模式]
2025-07-18 09:42:18 [wechat_handler] [INFO] [wechat_handler.py:292]  开始监听微信消息
2025-07-18 09:42:18 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-18 09:42:18 [main] [INFO] [run.py:150]  💡 提示:
2025-07-18 09:42:18 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-18 09:42:18 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-18 09:42:18 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-18 09:42:19 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-18 09:42:22 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-18 09:42:22 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:42:22 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:42:22 [wxauto] [DEBUG] [msg.py:80]  content: 你好呀！很高兴见到你～有什么需要帮忙的吗？我这里有很多好东西哦，欢迎随时找我聊聊 😊, length: 8
2025-07-18 09:42:22 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:42:22 [wxauto] [DEBUG] [msg.py:80]  content: 嗨！有什么想了解的吗？我在这儿呢，随时帮你解答！, length: 8
2025-07-18 09:42:22 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:42:22 [wxauto] [DEBUG] [msg.py:80]  content: 你好呀！有什么需要帮忙的吗？交个朋友，我来帮你选选适合的产品！, length: 8
2025-07-18 09:42:22 [wxauto] [DEBUG] [msg.py:80]  content: 手机, length: 8
2025-07-18 09:42:22 [wechat_handler] [DEBUG] [wechat_handler.py:389]  跳过消息: self - 你好呀！有什么需要帮忙的吗？交个朋友，我来帮你选选适合的产品... (ID: 0970a657)
2025-07-18 09:42:22 [wechat_handler] [DEBUG] [wechat_handler.py:389]  跳过消息: system - 以下为新消息... (ID: 10e4c388)
2025-07-18 09:42:22 [wechat_handler] [DEBUG] [wechat_handler.py:425]  全局监听模式，处理消息: 樂
2025-07-18 09:42:22 [wechat_handler] [INFO] [wechat_handler.py:384]  处理消息: 樂 - 手机... (ID: f01fcb35)
2025-07-18 09:42:22 [wechat_handler] [DEBUG] [wechat_handler.py:454]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:42:22 [wechat_handler] [DEBUG] [wechat_handler.py:510]  消息解析结果 - 发送者: 樂, 内容: 手机..., 类型: text
2025-07-18 09:42:22 [wechat_handler] [INFO] [wechat_handler.py:542]  消息详细信息 - 类型: text, 内容: 手机, 发送者: 樂
2025-07-18 09:42:22 [wechat_handler] [INFO] [wechat_handler.py:543]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:42:22 [wechat_handler] [INFO] [wechat_handler.py:625]  收到消息 - 发送者: 樂, 内容: 手机...
2025-07-18 09:42:22 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: 手机
2025-07-18 09:42:22 [jieba] [DEBUG] [__init__.py:113]  Building prefix dict from the default dictionary ...
2025-07-18 09:42:23 [jieba] [DEBUG] [__init__.py:146]  Dumping model to file cache C:\Users\<USER>\AppData\Local\Temp\jieba.cache
2025-07-18 09:42:23 [jieba] [DEBUG] [__init__.py:164]  Loading model cost 0.775 seconds.
2025-07-18 09:42:23 [jieba] [DEBUG] [__init__.py:166]  Prefix dict has been built successfully.
2025-07-18 09:42:23 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:75]  产品匹配成功: 找到 1 个产品
2025-07-18 09:42:23 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:82]  产品回复生成: 文本 141 字符, 图片 1 张
2025-07-18 09:42:25 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: 樂, False, False, 0.5
2025-07-18 09:42:28 [wechat_handler] [DEBUG] [wechat_handler.py:859]  消息已发送到: 樂
2025-07-18 09:42:28 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: 樂, False, False, 0.5
2025-07-18 09:42:30 [wechat_handler] [INFO] [wechat_handler.py:690]  已发送产品图片: phone_a1.jpg
2025-07-18 09:42:31 [wechat_handler] [INFO] [wechat_handler.py:646]  已回复: 推荐你这款产品：

🛍️ 1. 智能手机A1
💰 价格：¥2999
📝 描述：6.1寸全面屏，128... (图片: 1张)
2025-07-18 09:42:33 [wxauto] [DEBUG] [msg.py:80]  content: 手机, length: 8
2025-07-18 09:42:33 [wxauto] [DEBUG] [msg.py:80]  content: 推荐你这款产品：

🛍 1. 智能手机A1
💰 价格：¥2999
📝 描述：6.1寸全面屏，128GB存储，5000mAh大电池，支持快充
ℹ 详情：处理器：骁龙888
内存：8GB
存储：128GB
屏幕：6.1寸OLED
电池：5000mAh
摄像头：5000万像素三摄
, length: 8
2025-07-18 09:42:33 [wxauto] [DEBUG] [msg.py:80]  content: [文件], length: 21
2025-07-18 09:42:33 [wechat_handler] [DEBUG] [wechat_handler.py:389]  跳过消息: system - 以下为新消息... (ID: 07243f89)
2025-07-18 09:42:33 [wechat_handler] [DEBUG] [wechat_handler.py:425]  全局监听模式，处理消息: 樂
2025-07-18 09:42:33 [wechat_handler] [INFO] [wechat_handler.py:384]  处理消息: 樂 - 手机... (ID: 05178abd)
2025-07-18 09:42:33 [wechat_handler] [DEBUG] [wechat_handler.py:454]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:42:33 [wechat_handler] [DEBUG] [wechat_handler.py:510]  消息解析结果 - 发送者: 樂, 内容: 手机..., 类型: text
2025-07-18 09:42:33 [wechat_handler] [INFO] [wechat_handler.py:542]  消息详细信息 - 类型: text, 内容: 手机, 发送者: 樂
2025-07-18 09:42:33 [wechat_handler] [INFO] [wechat_handler.py:543]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:42:33 [wechat_handler] [INFO] [wechat_handler.py:625]  收到消息 - 发送者: 樂, 内容: 手机...
2025-07-18 09:42:33 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: 手机
2025-07-18 09:42:33 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:75]  产品匹配成功: 找到 1 个产品
2025-07-18 09:42:33 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:82]  产品回复生成: 文本 141 字符, 图片 1 张
2025-07-18 09:42:35 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: 樂, False, False, 0.5
2025-07-18 09:42:37 [wechat_handler] [DEBUG] [wechat_handler.py:859]  消息已发送到: 樂
2025-07-18 09:42:37 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: 樂, False, False, 0.5
2025-07-18 09:42:39 [wechat_handler] [INFO] [wechat_handler.py:690]  已发送产品图片: phone_a1.jpg
2025-07-18 09:42:40 [wechat_handler] [INFO] [wechat_handler.py:646]  已回复: 推荐你这款产品：

🛍️ 1. 智能手机A1
💰 价格：¥2999
📝 描述：6.1寸全面屏，128... (图片: 1张)
2025-07-18 09:42:40 [wechat_handler] [DEBUG] [wechat_handler.py:389]  跳过消息: self - 推荐你这款产品：

🛍 1. 智能手机A1
💰 价格：¥29... (ID: bd3b8229)
2025-07-18 09:42:40 [wechat_handler] [DEBUG] [wechat_handler.py:389]  跳过消息: self - [文件]... (ID: 4011174d)
2025-07-18 09:42:42 [wxauto] [DEBUG] [msg.py:80]  content: 推荐你这款产品：

🛍 1. 智能手机A1
💰 价格：¥2999
📝 描述：6.1寸全面屏，128GB存储，5000mAh大电池，支持快充
ℹ 详情：处理器：骁龙888
内存：8GB
存储：128GB
屏幕：6.1寸OLED
电池：5000mAh
摄像头：5000万像素三摄
, length: 8
2025-07-18 09:42:42 [wechat_handler] [DEBUG] [wechat_handler.py:389]  跳过消息: self - 推荐你这款产品：

🛍 1. 智能手机A1
💰 价格：¥29... (ID: fc1cd38e)
2025-07-18 09:42:44 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-18 09:42:47 [wxauto] [DEBUG] [main.py:239]  没有新消息
2025-07-18 09:42:47 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:42:47 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:42:47 [wxauto] [DEBUG] [msg.py:80]  content: 你好呀！很高兴见到你～有什么需要帮忙的吗？我这里有很多好东西哦，欢迎随时找我聊聊 😊, length: 8
2025-07-18 09:42:47 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:42:47 [wxauto] [DEBUG] [msg.py:80]  content: 嗨！有什么想了解的吗？我在这儿呢，随时帮你解答！, length: 8
2025-07-18 09:42:47 [wxauto] [DEBUG] [msg.py:80]  content: 你好, length: 8
2025-07-18 09:42:47 [wxauto] [DEBUG] [msg.py:80]  content: 你好呀！有什么需要帮忙的吗？交个朋友，我来帮你选选适合的产品！, length: 8
2025-07-18 09:42:47 [wxauto] [DEBUG] [msg.py:80]  content: 手机, length: 8
2025-07-18 09:42:47 [wxauto] [DEBUG] [msg.py:80]  content: 手机, length: 8
2025-07-18 09:42:48 [wxauto] [DEBUG] [msg.py:80]  content: 推荐你这款产品：

🛍 1. 智能手机A1
💰 价格：¥2999
📝 描述：6.1寸全面屏，128GB存储，5000mAh大电池，支持快充
ℹ 详情：处理器：骁龙888
内存：8GB
存储：128GB
屏幕：6.1寸OLED
电池：5000mAh
摄像头：5000万像素三摄
, length: 8
2025-07-18 09:42:48 [wxauto] [DEBUG] [msg.py:80]  content: [文件], length: 21
2025-07-18 09:42:48 [wxauto] [DEBUG] [msg.py:80]  content: 推荐你这款产品：

🛍 1. 智能手机A1
💰 价格：¥2999
📝 描述：6.1寸全面屏，128GB存储，5000mAh大电池，支持快充
ℹ 详情：处理器：骁龙888
内存：8GB
存储：128GB
屏幕：6.1寸OLED
电池：5000mAh
摄像头：5000万像素三摄
, length: 8
2025-07-18 09:42:48 [wechat_handler] [DEBUG] [wechat_handler.py:389]  跳过消息: self - 推荐你这款产品：

🛍 1. 智能手机A1
💰 价格：¥29... (ID: 44251cf5)
2025-07-18 09:42:48 [wechat_handler] [DEBUG] [wechat_handler.py:389]  跳过消息: self - [文件]... (ID: 5a29af79)
2025-07-18 09:42:48 [wechat_handler] [DEBUG] [wechat_handler.py:377]  跳过重复消息: 44251cf5d7c84da4efa37b932d4625b7
2025-07-18 09:42:48 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
