2025-07-18 08:58:57 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 08:58:57 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-18 08:59:02 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-18 08:59:02 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-18 08:59:02 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-18 08:59:02 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-18 08:59:02 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-18 08:59:02 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-18 08:59:02 [main] [INFO] [run.py:150]  💡 提示:
2025-07-18 08:59:02 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-18 08:59:02 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-18 08:59:02 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-18 08:59:31 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-18 08:59:47 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 08:59:47 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-18 08:59:52 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-18 08:59:52 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-18 08:59:52 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-18 08:59:52 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-18 08:59:52 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-18 08:59:52 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-18 08:59:52 [main] [INFO] [run.py:150]  💡 提示:
2025-07-18 08:59:52 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-18 08:59:52 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-18 08:59:52 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-18 09:00:17 [wechat_handler] [DEBUG] [wechat_handler.py:370]  GetNewMessage失败: cannot access local variable 'length' where it is not associated with a value
2025-07-18 09:00:17 [wechat_handler] [DEBUG] [wechat_handler.py:405]  GetNewMessage其他错误: cannot access local variable 'length' where it is not associated with a value
2025-07-18 09:00:18 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: 他最近干的挺恶心， 商业化的很快， 票直接只卖1/3了， 剩下各种广告渠道出票, length: 10
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: [文件], length: 23
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: 这种逃掉没用的
引用  的消息 : [视频], length: 27
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: 抓到了电动车载人加装遮阳伞也就是五十块, length: 10
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: 没人抓的, length: 10
2025-07-18 09:00:19 [wxauto] [DEBUG] [chatbox.py:341]  获取2条新消息，基准消息内容为：GreyForest：没人抓的
2025-07-18 09:00:19 [wxauto] [DEBUG] [chatbox.py:290]  未匹配到基准消息，以最后一条消息为基准：没人抓的
2025-07-18 09:00:52 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
