2025-07-18 08:58:57 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 08:58:57 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-18 08:59:02 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-18 08:59:02 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-18 08:59:02 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-18 08:59:02 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-18 08:59:02 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-18 08:59:02 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-18 08:59:02 [main] [INFO] [run.py:150]  💡 提示:
2025-07-18 08:59:02 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-18 08:59:02 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-18 08:59:02 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-18 08:59:31 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-18 08:59:47 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 08:59:47 [wechat_handler] [DEBUG] [wechat_handler.py:143]  等待微信对象初始化...
2025-07-18 08:59:52 [wechat_handler] [DEBUG] [wechat_handler.py:203]  微信对象基本验证通过
2025-07-18 08:59:52 [wechat_handler] [INFO] [wechat_handler.py:156]  微信连接成功，当前用户: 樂
2025-07-18 08:59:52 [wechat_handler] [DEBUG] [wechat_handler.py:157]  微信对象验证通过
2025-07-18 08:59:52 [wechat_handler] [INFO] [wechat_handler.py:312]  开始消息监听循环... [全局监听模式]
2025-07-18 08:59:52 [wechat_handler] [INFO] [wechat_handler.py:290]  开始监听微信消息
2025-07-18 08:59:52 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-18 08:59:52 [main] [INFO] [run.py:150]  💡 提示:
2025-07-18 08:59:52 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-18 08:59:52 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-18 08:59:52 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-18 09:00:17 [wechat_handler] [DEBUG] [wechat_handler.py:370]  GetNewMessage失败: cannot access local variable 'length' where it is not associated with a value
2025-07-18 09:00:17 [wechat_handler] [DEBUG] [wechat_handler.py:405]  GetNewMessage其他错误: cannot access local variable 'length' where it is not associated with a value
2025-07-18 09:00:18 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: 他最近干的挺恶心， 商业化的很快， 票直接只卖1/3了， 剩下各种广告渠道出票, length: 10
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: [文件], length: 23
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: 这种逃掉没用的
引用  的消息 : [视频], length: 27
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: 抓到了电动车载人加装遮阳伞也就是五十块, length: 10
2025-07-18 09:00:19 [wxauto] [DEBUG] [msg.py:80]  content: 没人抓的, length: 10
2025-07-18 09:00:19 [wxauto] [DEBUG] [chatbox.py:341]  获取2条新消息，基准消息内容为：GreyForest：没人抓的
2025-07-18 09:00:19 [wxauto] [DEBUG] [chatbox.py:290]  未匹配到基准消息，以最后一条消息为基准：没人抓的
2025-07-18 09:00:52 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-18 09:09:31 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 09:09:31 [wechat_handler] [DEBUG] [wechat_handler.py:144]  等待微信对象初始化...
2025-07-18 09:09:36 [wechat_handler] [DEBUG] [wechat_handler.py:204]  微信对象基本验证通过
2025-07-18 09:09:36 [wechat_handler] [INFO] [wechat_handler.py:157]  微信连接成功，当前用户: 樂
2025-07-18 09:09:36 [wechat_handler] [DEBUG] [wechat_handler.py:158]  微信对象验证通过
2025-07-18 09:09:36 [wechat_handler] [INFO] [wechat_handler.py:313]  开始消息监听循环... [全局监听模式]
2025-07-18 09:09:36 [wechat_handler] [INFO] [wechat_handler.py:291]  开始监听微信消息
2025-07-18 09:09:36 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-18 09:09:36 [main] [INFO] [run.py:150]  💡 提示:
2025-07-18 09:09:36 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-18 09:09:36 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-18 09:09:36 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-18 09:09:37 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-18 09:09:38 [wxauto] [DEBUG] [chatbox.py:305]  消息列表不存在，返回空列表
2025-07-18 09:09:49 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-18 09:09:50 [wxauto] [DEBUG] [msg.py:80]  content: 🔥【天天领券】20元券包速领, length: 19
2025-07-18 09:09:50 [wxauto] [DEBUG] [msg.py:80]  content: 🧧今日社群福利请查收
💥点击领取 享超值优惠
🍲美食团购🥤外卖🎮休闲玩乐
✅一键拥有 在家外出都能用
—— —— —— —— ——
📢先领券再下单❗❗
#小程序://惠省/领神券/gZWzJpzDkviG5Em
🛒低价好货随心选
#小程序://惠省/低价/5D59r6bSMtMGEmq, length: 10
2025-07-18 09:09:50 [wxauto] [DEBUG] [chatbox.py:341]  获取7条新消息，基准消息内容为：吃喝玩乐福利君：🧧今日社群福利请查收﻿💥点击领取 享超值优惠﻿🍲美食团购🥤外卖🎮休闲玩乐﻿✅一键拥有 在家外出都能用﻿—— —— —— —— ——﻿📢先领券再下单❗❗﻿#小程序://惠省/领神券/gZWzJpzDkviG5Em﻿🛒低价好货随心选﻿#小程序://惠省/低价/5D59r6bSMtMGEmq
2025-07-18 09:09:50 [wxauto] [DEBUG] [chatbox.py:290]  未匹配到基准消息，以最后一条消息为基准：🧧今日社群福利请查收
💥点击领取 享超值优惠
🍲美食团购🥤外卖🎮休闲玩乐
✅一键拥有 在家外出都能用
—— —— —— —— ——
📢先领券再下单❗❗
#小程序://惠省/领神券/gZWzJpzDkviG5Em
🛒低价好货随心选
#小程序://惠省/低价/5D59r6bSMtMGEmq
2025-07-18 09:09:50 [wxauto] [DEBUG] [chatbox.py:299]  未匹配到第7条消息，返回空列表
2025-07-18 09:09:50 [wxauto] [DEBUG] [msg.py:80]  content: 🔥【天天领券】20元券包速领, length: 19
2025-07-18 09:09:50 [wxauto] [DEBUG] [msg.py:80]  content: 🧧今日社群福利请查收
💥点击领取 享超值优惠
🍲美食团购🥤外卖🎮休闲玩乐
✅一键拥有 在家外出都能用
—— —— —— —— ——
📢先领券再下单❗❗
#小程序://惠省/领神券/gZWzJpzDkviG5Em
🛒低价好货随心选
#小程序://惠省/低价/5D59r6bSMtMGEmq, length: 10
2025-07-18 09:09:50 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: base
2025-07-18 09:09:50 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: base - 9:01... (ID: 4273f0d4)
2025-07-18 09:09:50 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: OtherMessage, 模块: wxauto.msgs.type
2025-07-18 09:09:50 [wechat_handler] [DEBUG] [wechat_handler.py:504]  忽略其他类型消息: 9:01
2025-07-18 09:09:50 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: 吃喝玩乐福利君
2025-07-18 09:09:50 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: 吃喝玩乐福利君 - 🔥【天天领券】20元券包速领... (ID: e986f513)
2025-07-18 09:09:50 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: FriendOtherMessage, 模块: wxauto.msgs.friend
2025-07-18 09:09:50 [wechat_handler] [DEBUG] [wechat_handler.py:504]  忽略其他类型消息: 🔥【天天领券】20元券包速领
2025-07-18 09:09:50 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: 吃喝玩乐福利君
2025-07-18 09:09:50 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: 吃喝玩乐福利君 - 🧧今日社群福利请查收
💥点击领取 享超值优惠
🍲美食团购🥤外... (ID: f2ffac36)
2025-07-18 09:09:50 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:09:50 [wechat_handler] [DEBUG] [wechat_handler.py:509]  消息解析结果 - 发送者: 吃喝玩乐福利君, 内容: 🧧今日社群福利请查收
💥点击领取 享超值优惠
🍲美食团购🥤外卖🎮休闲玩乐
✅一键拥有 在家外出都能用..., 类型: text
2025-07-18 09:09:50 [wechat_handler] [INFO] [wechat_handler.py:541]  消息详细信息 - 类型: text, 内容: 🧧今日社群福利请查收
💥点击领取 享超值优惠
🍲美食团购🥤外卖🎮休闲玩乐
✅一键拥有 在家外出都能用
—— —— —— —— ——
📢先领券再下单❗❗
#小程序://惠省/领神券/gZWzJpzDkv, 发送者: 吃喝玩乐福利君
2025-07-18 09:09:50 [wechat_handler] [INFO] [wechat_handler.py:542]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:09:50 [wechat_handler] [INFO] [wechat_handler.py:624]  收到消息 - 发送者: 吃喝玩乐福利君, 内容: 🧧今日社群福利请查收
💥点击领取 享超值优惠
🍲美食团购🥤外卖🎮休闲玩乐
✅一键拥有 在家外出都能用...
2025-07-18 09:09:50 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: 🧧今日社群福利请查收
💥点击领取 享超值优惠
🍲美食团购🥤外卖🎮休闲玩乐
✅一键拥有 在家外出都能用
—— —— —— —— ——
📢先领券再下单❗❗
#小程序://惠省/领神券/gZWzJpzDkviG5Em
🛒低价好货随心选
#小程序://惠省/低价/5D59r6bSMtMGEmq
2025-07-18 09:09:50 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:62]  FAQ匹配成功: 有什么优惠活动吗？ (分数: 1.00)
2025-07-18 09:09:52 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: 吃喝玩乐福利君, False, False, 0.5
2025-07-18 09:09:59 [wechat_handler] [ERROR] [wechat_handler.py:860]  无法切换到聊天窗口: 吃喝玩乐福利君
2025-07-18 09:09:59 [wechat_handler] [INFO] [wechat_handler.py:645]  已回复: 我们经常有各种优惠活动，建议您关注我们的官方公众号获取最新优惠信息。新用户首单可享受9折优惠。... (图片: 0张)
2025-07-18 09:10:02 [wxauto] [DEBUG] [sessionbox.py:165]  回到会话列表顶部
2025-07-18 09:10:06 [wxauto] [DEBUG] [main.py:236]  翻页会话列表获取新消息
2025-07-18 09:10:07 [wxauto] [DEBUG] [msg.py:80]  content: [动画表情], length: 10
2025-07-18 09:10:07 [wxauto] [DEBUG] [msg.py:80]  content: [视频号], length: 20
2025-07-18 09:10:07 [wxauto] [DEBUG] [chatbox.py:341]  获取2条新消息，基准消息内容为：4栋2004：[视频号]新京报的动态
2025-07-18 09:10:07 [wxauto] [DEBUG] [chatbox.py:290]  未匹配到基准消息，以最后一条消息为基准：[视频号]
2025-07-18 09:10:07 [wxauto] [DEBUG] [msg.py:80]  content: [动画表情], length: 10
2025-07-18 09:10:07 [wxauto] [DEBUG] [msg.py:80]  content: [视频号], length: 20
2025-07-18 09:10:07 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: এ᭄敏敏ོꦿృ༊
2025-07-18 09:10:07 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: এ᭄敏敏ོꦿృ༊ - [动画表情]... (ID: af146522)
2025-07-18 09:10:07 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:10:07 [wechat_handler] [DEBUG] [wechat_handler.py:509]  消息解析结果 - 发送者: এ᭄敏敏ོꦿృ༊, 内容: [动画表情]..., 类型: text
2025-07-18 09:10:07 [wechat_handler] [INFO] [wechat_handler.py:541]  消息详细信息 - 类型: text, 内容: [动画表情], 发送者: এ᭄敏敏ོꦿృ༊
2025-07-18 09:10:07 [wechat_handler] [INFO] [wechat_handler.py:542]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:10:07 [wechat_handler] [INFO] [wechat_handler.py:624]  收到消息 - 发送者: এ᭄敏敏ོꦿృ༊, 内容: [动画表情]...
2025-07-18 09:10:07 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: [动画表情]
2025-07-18 09:10:10 [llm_service] [INFO] [llm_service.py:36]  LLM客户端初始化成功
2025-07-18 09:10:10 [openai._base_client] [DEBUG] [_base_client.py:482]  Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-88e4fef3-e049-4657-81b2-9cd4ec03c117', 'json_data': {'messages': [{'role': 'system', 'content': '你是一个热情友好的销售顾问，对各类产品都很了解。\n\n你的特点：\n- 说话自然随和，就像朋友聊天一样\n- 对产品很熟悉，能给出实用的建议\n- 会根据用户需求推荐合适的产品\n- 不会说自己是AI或机器人\n- 不会提到联系客服或人工服务\n\n回复风格：\n- 用口语化的表达，比如这款挺不错的、我觉得、建议你看看\n- 简洁明了，一般20到50字就够了\n- 语气轻松友好，避免过于正式\n- 如果不确定，可以说我了解的不多或这个我不太清楚\n- 重点突出产品的实用价值和使用体验\n\n记住：你就是一个懂产品的朋友，自然地和用户交流就好。'}, {'role': 'user', 'content': '[动画表情]'}], 'model': 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B', 'max_tokens': 50, 'temperature': 0.7}}
2025-07-18 09:10:10 [openai._base_client] [DEBUG] [_base_client.py:968]  Sending HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions
2025-07-18 09:10:10 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.started host='127.0.0.1' port=8081 local_address=None timeout=5.0 socket_options=None
2025-07-18 09:10:10 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000161DCE28910>
2025-07-18 09:10:10 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:10:10 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:10:10 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'CONNECT']>
2025-07-18 09:10:10 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:10:10 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:10:10 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-18 09:10:10 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.started ssl_context=<ssl.SSLContext object at 0x00000161DC9895B0> server_hostname='api.siliconflow.cn' timeout=5.0
2025-07-18 09:10:12 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000161DCE2A750>
2025-07-18 09:10:12 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'POST']>
2025-07-18 09:10:12 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:10:12 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'POST']>
2025-07-18 09:10:12 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:10:12 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'POST']>
2025-07-18 09:10:22 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 18 Jul 2025 01:10:25 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Content-Length', b'1334'), (b'Connection', b'keep-alive')])
2025-07-18 09:10:22 [httpx] [INFO] [_client.py:1025]  HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-18 09:10:22 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.started request=<Request [b'POST']>
2025-07-18 09:10:22 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.complete
2025-07-18 09:10:22 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.started
2025-07-18 09:10:22 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.complete
2025-07-18 09:10:22 [openai._base_client] [DEBUG] [_base_client.py:1006]  HTTP Response: POST https://api.siliconflow.cn/v1/chat/completions "200 OK" Headers({'date': 'Fri, 18 Jul 2025 01:10:25 GMT', 'content-type': 'application/json; charset=utf-8', 'content-length': '1334', 'connection': 'keep-alive'})
2025-07-18 09:10:22 [openai._base_client] [DEBUG] [_base_client.py:1014]  request_id: None
2025-07-18 09:10:22 [llm_service] [INFO] [llm_service.py:91]  AI回复生成成功，长度: 29
2025-07-18 09:10:22 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:107]  使用AI回复
2025-07-18 09:10:24 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: এ᭄敏敏ོꦿృ༊, False, False, 0.5
2025-07-18 09:10:31 [wechat_handler] [ERROR] [wechat_handler.py:860]  无法切换到聊天窗口: এ᭄敏敏ོꦿృ༊
2025-07-18 09:10:31 [wechat_handler] [INFO] [wechat_handler.py:645]  已回复: 哇，看到你的表情好开心！😄 有什么想了解的吗？咱们慢慢聊～... (图片: 0张)
2025-07-18 09:10:31 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: base
2025-07-18 09:10:31 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: base - 8:43... (ID: 90930679)
2025-07-18 09:10:31 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: OtherMessage, 模块: wxauto.msgs.type
2025-07-18 09:10:31 [wechat_handler] [DEBUG] [wechat_handler.py:504]  忽略其他类型消息: 8:43
2025-07-18 09:10:31 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: 张记贝壳批发联系电话
2025-07-18 09:10:31 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: 张记贝壳批发联系电话 - [视频号]... (ID: dbfd3056)
2025-07-18 09:10:31 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: FriendOtherMessage, 模块: wxauto.msgs.friend
2025-07-18 09:10:31 [wechat_handler] [DEBUG] [wechat_handler.py:504]  忽略其他类型消息: [视频号]
2025-07-18 09:10:34 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-18 09:10:34 [wxauto] [DEBUG] [chatbox.py:341]  获取1条新消息，基准消息内容为：玩《龙珠激斗》正版龙珠IP手游领50连抽
2025-07-18 09:10:34 [wxauto] [DEBUG] [chatbox.py:290]  未匹配到基准消息，以最后一条消息为基准：
2025-07-18 09:10:34 [wxauto] [DEBUG] [chatbox.py:299]  未匹配到第1条消息，返回空列表
2025-07-18 09:10:35 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: base
2025-07-18 09:10:35 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: base - 8:25... (ID: cc7f51c8)
2025-07-18 09:10:35 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: OtherMessage, 模块: wxauto.msgs.type
2025-07-18 09:10:35 [wechat_handler] [DEBUG] [wechat_handler.py:504]  忽略其他类型消息: 8:25
2025-07-18 09:10:37 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-18 09:10:38 [wxauto] [DEBUG] [chatbox.py:341]  获取6条新消息，基准消息内容为：橘子（0757-86770225）：[图片]
2025-07-18 09:10:38 [wxauto] [DEBUG] [chatbox.py:290]  未匹配到基准消息，以最后一条消息为基准：
2025-07-18 09:10:38 [wxauto] [DEBUG] [chatbox.py:299]  未匹配到第6条消息，返回空列表
2025-07-18 09:10:38 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: base
2025-07-18 09:10:38 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: base - 8:25... (ID: 0c9d1a49)
2025-07-18 09:10:38 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: OtherMessage, 模块: wxauto.msgs.type
2025-07-18 09:10:38 [wechat_handler] [DEBUG] [wechat_handler.py:504]  忽略其他类型消息: 8:25
2025-07-18 09:10:41 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-18 09:10:41 [wxauto] [DEBUG] [msg.py:80]  content: 真好
引用  的消息 : 打完球归家。。, length: 18
2025-07-18 09:10:41 [wxauto] [DEBUG] [msg.py:80]  content: 你一支公来, length: 10
2025-07-18 09:10:42 [wxauto] [DEBUG] [msg.py:80]  content: 卧槽，电信掉网了, length: 10
2025-07-18 09:10:42 [wxauto] [DEBUG] [msg.py:80]  content: 又好了, length: 10
2025-07-18 09:10:42 [wxauto] [DEBUG] [msg.py:80]  content: [呲牙][呲牙][呲牙], length: 10
2025-07-18 09:10:42 [wxauto] [DEBUG] [msg.py:80]  content: [动画表情], length: 10
2025-07-18 09:10:42 [wxauto] [DEBUG] [chatbox.py:341]  获取2条新消息，基准消息内容为：龙光6-2503黄沙海鲜金金：[动画表情]
2025-07-18 09:10:42 [wxauto] [DEBUG] [chatbox.py:290]  未匹配到基准消息，以最后一条消息为基准：[动画表情]
2025-07-18 09:10:42 [wxauto] [DEBUG] [msg.py:80]  content: 继续瘦点先, length: 10
2025-07-18 09:10:42 [wxauto] [DEBUG] [msg.py:80]  content: 我现在不到120[苦涩], length: 10
2025-07-18 09:10:42 [wxauto] [DEBUG] [msg.py:80]  content: 估计这段时间胖了。120+, length: 10
2025-07-18 09:10:42 [wxauto] [DEBUG] [msg.py:80]  content: 还没以前初中的时候重, length: 10
2025-07-18 09:10:42 [wxauto] [DEBUG] [msg.py:80]  content: @এ᭄敏敏ོꦿృ༊ 早戒了, length: 10
2025-07-18 09:10:43 [wxauto] [DEBUG] [msg.py:80]  content: 从夜工作者 都不会胖
引用  的消息 : 我现在不到120[苦涩], length: 18
2025-07-18 09:10:43 [wxauto] [DEBUG] [msg.py:80]  content: 听说越老越瘦
引用  的消息 : 还没以前初中的时候重, length: 18
2025-07-18 09:10:43 [wxauto] [DEBUG] [msg.py:80]  content: [动画表情], length: 10
2025-07-18 09:10:43 [wxauto] [DEBUG] [msg.py:80]  content: 是的 缩水
引用  的消息 : 听说越老越瘦, length: 18
2025-07-18 09:10:43 [wxauto] [DEBUG] [msg.py:80]  content: 18岁，老吗！, length: 10
2025-07-18 09:10:43 [wxauto] [DEBUG] [msg.py:80]  content: 老大过几年都要娶媳妇了
引用  的消息 : 18岁，老吗！, length: 18
2025-07-18 09:10:43 [wxauto] [DEBUG] [msg.py:80]  content: 哇，你同结哥系咪约好的，一齐戒酒
引用  的消息 : @এ᭄敏敏ོꦿృ༊ 早戒了, length: 18
2025-07-18 09:10:43 [wxauto] [DEBUG] [msg.py:80]  content: 不可能, length: 10
2025-07-18 09:10:43 [wxauto] [DEBUG] [msg.py:80]  content: 昨晚才喝了
引用  的消息 : 哇，你同结哥系咪约好的，一齐戒酒, length: 18
2025-07-18 09:10:44 [wxauto] [DEBUG] [msg.py:80]  content: [捂脸][捂脸][捂脸], length: 10
2025-07-18 09:10:44 [wxauto] [DEBUG] [msg.py:80]  content: 没有喝多少
引用  的消息 : 昨晚才喝了, length: 18
2025-07-18 09:10:44 [wxauto] [DEBUG] [msg.py:80]  content: [动画表情], length: 10
2025-07-18 09:10:44 [wxauto] [DEBUG] [msg.py:80]  content: 你们日日酒池肉林夜夜笙歌令人羡慕。
引用  的消息 : 昨晚才喝了, length: 18
2025-07-18 09:10:44 [wxauto] [DEBUG] [msg.py:80]  content: 没
引用  的消息 : 你们日日酒池肉林夜夜笙歌令人羡慕。, length: 18
2025-07-18 09:10:44 [wxauto] [DEBUG] [msg.py:80]  content: 戒宵夜了, length: 10
2025-07-18 09:10:44 [wxauto] [DEBUG] [msg.py:80]  content: 酒也饮少了, length: 10
2025-07-18 09:10:44 [wxauto] [DEBUG] [msg.py:80]  content: 信你一成猪乸识上树，华哥一早就打定强心针叫啊勇不好浪费电。[Facepalm]
引用  的消息 : 没, length: 18
2025-07-18 09:10:44 [wxauto] [DEBUG] [msg.py:80]  content: 打完球归家。。, length: 10
2025-07-18 09:10:45 [wxauto] [DEBUG] [msg.py:80]  content: 真
引用  的消息 : 信你一成猪乸识上树，华哥一早就打定强心针叫啊勇不好浪费电。[Facepalm], length: 18
2025-07-18 09:10:45 [wxauto] [DEBUG] [msg.py:80]  content: 真好
引用  的消息 : 打完球归家。。, length: 18
2025-07-18 09:10:45 [wxauto] [DEBUG] [msg.py:80]  content: 你一支公来, length: 10
2025-07-18 09:10:45 [wxauto] [DEBUG] [msg.py:80]  content: 卧槽，电信掉网了, length: 10
2025-07-18 09:10:45 [wxauto] [DEBUG] [msg.py:80]  content: 又好了, length: 10
2025-07-18 09:10:45 [wxauto] [DEBUG] [msg.py:80]  content: [呲牙][呲牙][呲牙], length: 10
2025-07-18 09:10:45 [wxauto] [DEBUG] [msg.py:80]  content: [动画表情], length: 10
2025-07-18 09:10:45 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: base
2025-07-18 09:10:45 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: base - 8:06... (ID: 8b6a9f2f)
2025-07-18 09:10:45 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: OtherMessage, 模块: wxauto.msgs.type
2025-07-18 09:10:45 [wechat_handler] [DEBUG] [wechat_handler.py:504]  忽略其他类型消息: 8:06
2025-07-18 09:10:45 [wechat_handler] [DEBUG] [wechat_handler.py:424]  全局监听模式，处理消息: 龙光6-2503黄沙海鲜金金
2025-07-18 09:10:45 [wechat_handler] [INFO] [wechat_handler.py:383]  处理消息: 龙光6-2503黄沙海鲜金金 - [呲牙][呲牙][呲牙]... (ID: de0f177e)
2025-07-18 09:10:45 [wechat_handler] [DEBUG] [wechat_handler.py:453]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:10:45 [wechat_handler] [DEBUG] [wechat_handler.py:509]  消息解析结果 - 发送者: 龙光6-2503黄沙海鲜金金, 内容: [呲牙][呲牙][呲牙]..., 类型: text
2025-07-18 09:10:45 [wechat_handler] [INFO] [wechat_handler.py:541]  消息详细信息 - 类型: text, 内容: [呲牙][呲牙][呲牙], 发送者: 龙光6-2503黄沙海鲜金金
2025-07-18 09:10:45 [wechat_handler] [INFO] [wechat_handler.py:542]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:10:45 [wechat_handler] [INFO] [wechat_handler.py:624]  收到消息 - 发送者: 龙光6-2503黄沙海鲜金金, 内容: [呲牙][呲牙][呲牙]...
2025-07-18 09:10:45 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: [呲牙][呲牙][呲牙]
2025-07-18 09:10:46 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-18 09:10:47 [llm_service] [INFO] [llm_service.py:36]  LLM客户端初始化成功
2025-07-18 09:10:47 [openai._base_client] [DEBUG] [_base_client.py:482]  Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-b951e8a1-a12a-42c3-bb3e-a780d1f66a5b', 'json_data': {'messages': [{'role': 'system', 'content': '你是一个热情友好的销售顾问，对各类产品都很了解。\n\n你的特点：\n- 说话自然随和，就像朋友聊天一样\n- 对产品很熟悉，能给出实用的建议\n- 会根据用户需求推荐合适的产品\n- 不会说自己是AI或机器人\n- 不会提到联系客服或人工服务\n\n回复风格：\n- 用口语化的表达，比如这款挺不错的、我觉得、建议你看看\n- 简洁明了，一般20到50字就够了\n- 语气轻松友好，避免过于正式\n- 如果不确定，可以说我了解的不多或这个我不太清楚\n- 重点突出产品的实用价值和使用体验\n\n记住：你就是一个懂产品的朋友，自然地和用户交流就好。'}, {'role': 'system', 'content': '相关信息：龙光6-2503黄沙海鲜金金'}, {'role': 'user', 'content': '[呲牙][呲牙][呲牙]'}], 'model': 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B', 'max_tokens': 50, 'temperature': 0.7}}
2025-07-18 09:10:47 [openai._base_client] [DEBUG] [_base_client.py:968]  Sending HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions
2025-07-18 09:10:47 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.started host='127.0.0.1' port=8081 local_address=None timeout=5.0 socket_options=None
2025-07-18 09:10:47 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000161DCE49BD0>
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'CONNECT']>
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-18 09:10:47 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.started ssl_context=<ssl.SSLContext object at 0x00000161DCC26C30> server_hostname='api.siliconflow.cn' timeout=5.0
2025-07-18 09:10:47 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000161DCE4A890>
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'POST']>
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'POST']>
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:10:47 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'POST']>
2025-07-18 09:10:51 [main] [INFO] [run.py:29]  收到退出信号，正在关闭程序...
2025-07-18 09:10:51 [main] [INFO] [run.py:35]  程序已退出
2025-07-18 09:10:51 [main] [INFO] [run.py:176]  程序已退出
2025-07-18 09:16:15 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 09:16:19 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:43]  增强回复引擎（支持图片）初始化完成
2025-07-18 09:16:19 [wechat_handler] [INFO] [wechat_handler.py:63]  微信处理器初始化完成
2025-07-18 09:16:20 [wechat_handler] [INFO] [wechat_handler.py:139]  正在初始化微信连接... (尝试 1/5)
2025-07-18 09:16:20 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 09:16:20 [wechat_handler] [DEBUG] [wechat_handler.py:145]  等待微信对象初始化...
2025-07-18 09:16:25 [wechat_handler] [DEBUG] [wechat_handler.py:205]  微信对象基本验证通过
2025-07-18 09:16:25 [wechat_handler] [INFO] [wechat_handler.py:158]  微信连接成功，当前用户: 樂
2025-07-18 09:16:25 [wechat_handler] [DEBUG] [wechat_handler.py:159]  微信对象验证通过
2025-07-18 09:16:25 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 09:20:28 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 09:21:27 [wxauto] [DEBUG] [wx.py:31]  开始监听
2025-07-18 09:21:27 [wechat_handler] [DEBUG] [wechat_handler.py:145]  等待微信对象初始化...
2025-07-18 09:21:32 [wechat_handler] [DEBUG] [wechat_handler.py:205]  微信对象基本验证通过
2025-07-18 09:21:32 [wechat_handler] [INFO] [wechat_handler.py:158]  微信连接成功，当前用户: 樂
2025-07-18 09:21:32 [wechat_handler] [DEBUG] [wechat_handler.py:159]  微信对象验证通过
2025-07-18 09:21:32 [wechat_handler] [INFO] [wechat_handler.py:314]  开始消息监听循环... [全局监听模式]
2025-07-18 09:21:32 [wechat_handler] [INFO] [wechat_handler.py:292]  开始监听微信消息
2025-07-18 09:21:32 [main] [INFO] [run.py:149]  ✅ 微信客服机器人启动成功！
2025-07-18 09:21:32 [main] [INFO] [run.py:150]  💡 提示:
2025-07-18 09:21:32 [main] [INFO] [run.py:151]     - 按 Ctrl+C 退出程序
2025-07-18 09:21:32 [main] [INFO] [run.py:152]     - 运行 python web_config.py 打开配置界面
2025-07-18 09:21:32 [main] [INFO] [run.py:153]     - 确保微信PC版保持登录状态
2025-07-18 09:21:33 [wxauto] [DEBUG] [main.py:226]  当前会话列表获取新消息
2025-07-18 09:21:34 [wxauto] [DEBUG] [msg.py:80]  content: [图片], length: 11
2025-07-18 09:21:34 [wxauto] [DEBUG] [msg.py:80]  content: 我去西朗市场买 活鸡现杀[奸笑], length: 10
2025-07-18 09:21:34 [wxauto] [DEBUG] [msg.py:80]  content: 所以 不要盲目追捧 小胖做的很好, length: 10
2025-07-18 09:21:34 [wxauto] [DEBUG] [msg.py:80]  content: 加钱还能去骨头 [奸笑], length: 10
2025-07-18 09:21:34 [wxauto] [DEBUG] [chatbox.py:341]  获取3条新消息，基准消息内容为：5-2804恒：加钱还能去骨头 [奸笑]
2025-07-18 09:21:34 [wxauto] [DEBUG] [chatbox.py:290]  未匹配到基准消息，以最后一条消息为基准：加钱还能去骨头 [奸笑]
2025-07-18 09:21:34 [wxauto] [DEBUG] [msg.py:80]  content: [图片], length: 11
2025-07-18 09:21:34 [wxauto] [DEBUG] [msg.py:80]  content: 我去西朗市场买 活鸡现杀[奸笑], length: 10
2025-07-18 09:21:34 [wxauto] [DEBUG] [msg.py:80]  content: 所以 不要盲目追捧 小胖做的很好, length: 10
2025-07-18 09:21:34 [wxauto] [DEBUG] [msg.py:80]  content: 加钱还能去骨头 [奸笑], length: 10
2025-07-18 09:21:34 [wechat_handler] [DEBUG] [wechat_handler.py:425]  全局监听模式，处理消息: 许实大型数码喷绘
2025-07-18 09:21:34 [wechat_handler] [INFO] [wechat_handler.py:384]  处理消息: 许实大型数码喷绘 - 所以 不要盲目追捧 小胖做的很好... (ID: b257e428)
2025-07-18 09:21:34 [wechat_handler] [DEBUG] [wechat_handler.py:454]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:21:34 [wechat_handler] [DEBUG] [wechat_handler.py:510]  消息解析结果 - 发送者: 许实大型数码喷绘, 内容: 所以 不要盲目追捧 小胖做的很好..., 类型: text
2025-07-18 09:21:34 [wechat_handler] [INFO] [wechat_handler.py:542]  消息详细信息 - 类型: text, 内容: 所以 不要盲目追捧 小胖做的很好, 发送者: 许实大型数码喷绘
2025-07-18 09:21:34 [wechat_handler] [INFO] [wechat_handler.py:543]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:21:34 [wechat_handler] [INFO] [wechat_handler.py:625]  收到消息 - 发送者: 许实大型数码喷绘, 内容: 所以 不要盲目追捧 小胖做的很好...
2025-07-18 09:21:34 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: 所以 不要盲目追捧 小胖做的很好
2025-07-18 09:21:37 [llm_service] [INFO] [llm_service.py:36]  LLM客户端初始化成功
2025-07-18 09:21:37 [openai._base_client] [DEBUG] [_base_client.py:482]  Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-e08145ed-903d-4e27-b97f-aaabb9c08c83', 'json_data': {'messages': [{'role': 'system', 'content': '你是一个热情友好的销售顾问，对各类产品都很了解。\n\n你的特点：\n- 说话自然随和，就像朋友聊天一样\n- 对产品很熟悉，能给出实用的建议\n- 会根据用户需求推荐合适的产品\n- 不会说自己是AI或机器人\n- 不会提到联系客服或人工服务\n\n回复风格：\n- 用口语化的表达，比如这款挺不错的、我觉得、建议你看看\n- 简洁明了，一般20到50字就够了\n- 语气轻松友好，避免过于正式\n- 如果不确定，可以说我了解的不多或这个我不太清楚\n- 重点突出产品的实用价值和使用体验\n\n记住：你就是一个懂产品的朋友，自然地和用户交流就好。'}, {'role': 'user', 'content': '所以 不要盲目追捧 小胖做的很好'}], 'model': 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B', 'max_tokens': 50, 'temperature': 0.7}}
2025-07-18 09:21:37 [openai._base_client] [DEBUG] [_base_client.py:968]  Sending HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions
2025-07-18 09:21:37 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.started host='127.0.0.1' port=8081 local_address=None timeout=5.0 socket_options=None
2025-07-18 09:21:37 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002760EE5FB50>
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'CONNECT']>
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-18 09:21:37 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.started ssl_context=<ssl.SSLContext object at 0x000002760D9FD520> server_hostname='api.siliconflow.cn' timeout=5.0
2025-07-18 09:21:37 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002760EE69950>
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'POST']>
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'POST']>
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:21:37 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'POST']>
2025-07-18 09:21:59 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Fri, 18 Jul 2025 01:22:01 GMT'), (b'Content-Type', b'application/json; charset=utf-8'), (b'Content-Length', b'2101'), (b'Connection', b'keep-alive')])
2025-07-18 09:21:59 [httpx] [INFO] [_client.py:1025]  HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-18 09:21:59 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.started request=<Request [b'POST']>
2025-07-18 09:21:59 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_body.complete
2025-07-18 09:21:59 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.started
2025-07-18 09:21:59 [httpcore.http11] [DEBUG] [_trace.py:47]  response_closed.complete
2025-07-18 09:21:59 [openai._base_client] [DEBUG] [_base_client.py:1006]  HTTP Response: POST https://api.siliconflow.cn/v1/chat/completions "200 OK" Headers({'date': 'Fri, 18 Jul 2025 01:22:01 GMT', 'content-type': 'application/json; charset=utf-8', 'content-length': '2101', 'connection': 'keep-alive'})
2025-07-18 09:21:59 [openai._base_client] [DEBUG] [_base_client.py:1014]  request_id: None
2025-07-18 09:21:59 [llm_service] [INFO] [llm_service.py:91]  AI回复生成成功，长度: 57
2025-07-18 09:21:59 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:107]  使用AI回复
2025-07-18 09:22:01 [wxauto] [DEBUG] [sessionbox.py:63]  切换聊天窗口: 许实大型数码喷绘, False, False, 0.5
2025-07-18 09:22:09 [wechat_handler] [DEBUG] [wechat_handler.py:859]  消息已发送到: 许实大型数码喷绘
2025-07-18 09:22:09 [wechat_handler] [INFO] [wechat_handler.py:646]  已回复: 哈哈，你说得对！买东西确实要结合自己的需求来选，不能光看别人说好就入手。有具体想了解的产品吗？我可以... (图片: 0张)
2025-07-18 09:22:09 [wechat_handler] [DEBUG] [wechat_handler.py:425]  全局监听模式，处理消息: base
2025-07-18 09:22:09 [wechat_handler] [INFO] [wechat_handler.py:384]  处理消息: base - 9:17... (ID: d66670a3)
2025-07-18 09:22:09 [wechat_handler] [DEBUG] [wechat_handler.py:454]  收到消息对象 - 类型: OtherMessage, 模块: wxauto.msgs.type
2025-07-18 09:22:09 [wechat_handler] [DEBUG] [wechat_handler.py:505]  忽略其他类型消息: 9:17
2025-07-18 09:22:09 [wechat_handler] [DEBUG] [wechat_handler.py:425]  全局监听模式，处理消息: 5-2804恒
2025-07-18 09:22:09 [wechat_handler] [INFO] [wechat_handler.py:384]  处理消息: 5-2804恒 - 加钱还能去骨头 [奸笑]... (ID: 19d39d43)
2025-07-18 09:22:09 [wechat_handler] [DEBUG] [wechat_handler.py:454]  收到消息对象 - 类型: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:22:09 [wechat_handler] [DEBUG] [wechat_handler.py:510]  消息解析结果 - 发送者: 5-2804恒, 内容: 加钱还能去骨头 [奸笑]..., 类型: text
2025-07-18 09:22:09 [wechat_handler] [INFO] [wechat_handler.py:542]  消息详细信息 - 类型: text, 内容: 加钱还能去骨头 [奸笑], 发送者: 5-2804恒
2025-07-18 09:22:09 [wechat_handler] [INFO] [wechat_handler.py:543]  消息对象类名: FriendTextMessage, 模块: wxauto.msgs.friend
2025-07-18 09:22:09 [wechat_handler] [INFO] [wechat_handler.py:625]  收到消息 - 发送者: 5-2804恒, 内容: 加钱还能去骨头 [奸笑]...
2025-07-18 09:22:09 [src.bot.enhanced_reply_engine_with_images] [INFO] [enhanced_reply_engine_with_images.py:57]  处理消息: 加钱还能去骨头 [奸笑]
2025-07-18 09:22:11 [llm_service] [INFO] [llm_service.py:36]  LLM客户端初始化成功
2025-07-18 09:22:11 [openai._base_client] [DEBUG] [_base_client.py:482]  Request options: {'method': 'post', 'url': '/chat/completions', 'files': None, 'idempotency_key': 'stainless-python-retry-2fd05352-7ac0-482d-932c-4a9d37549e0c', 'json_data': {'messages': [{'role': 'system', 'content': '你是一个热情友好的销售顾问，对各类产品都很了解。\n\n你的特点：\n- 说话自然随和，就像朋友聊天一样\n- 对产品很熟悉，能给出实用的建议\n- 会根据用户需求推荐合适的产品\n- 不会说自己是AI或机器人\n- 不会提到联系客服或人工服务\n\n回复风格：\n- 用口语化的表达，比如这款挺不错的、我觉得、建议你看看\n- 简洁明了，一般20到50字就够了\n- 语气轻松友好，避免过于正式\n- 如果不确定，可以说我了解的不多或这个我不太清楚\n- 重点突出产品的实用价值和使用体验\n\n记住：你就是一个懂产品的朋友，自然地和用户交流就好。'}, {'role': 'user', 'content': '加钱还能去骨头 [奸笑]'}], 'model': 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B', 'max_tokens': 50, 'temperature': 0.7}}
2025-07-18 09:22:11 [openai._base_client] [DEBUG] [_base_client.py:968]  Sending HTTP Request: POST https://api.siliconflow.cn/v1/chat/completions
2025-07-18 09:22:11 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.started host='127.0.0.1' port=8081 local_address=None timeout=5.0 socket_options=None
2025-07-18 09:22:11 [httpcore.connection] [DEBUG] [_trace.py:47]  connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002760EE5EC10>
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'CONNECT']>
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'CONNECT']>
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-07-18 09:22:11 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.started ssl_context=<ssl.SSLContext object at 0x000002760EC66BA0> server_hostname='api.siliconflow.cn' timeout=5.0
2025-07-18 09:22:11 [httpcore.proxy] [DEBUG] [_trace.py:47]  start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x000002760EE933D0>
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.started request=<Request [b'POST']>
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_headers.complete
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.started request=<Request [b'POST']>
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  send_request_body.complete
2025-07-18 09:22:11 [httpcore.http11] [DEBUG] [_trace.py:47]  receive_response_headers.started request=<Request [b'POST']>
