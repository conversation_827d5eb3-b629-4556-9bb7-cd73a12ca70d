# WChat私域自动化机器人 - 部署包说明

## 🎉 打包完成！

您的WChat私域自动化机器人已成功打包，可以在其他电脑上部署使用！

## 📦 部署包信息

### 📁 包含内容
- **完整源代码** - 所有核心功能模块
- **配置文件** - 已清理敏感信息的配置
- **数据文件** - FAQ和产品数据库
- **产品图片** - 示例产品图片
- **启动脚本** - 多种启动方式
- **安装工具** - 自动安装依赖
- **使用文档** - 详细使用说明

### 🎯 功能特点
- ✅ **AI智能聊天** - 基于硅基流动API的自然对话
- ✅ **产品推荐** - 智能产品匹配+自动图片发送
- ✅ **语音消息** - 语音转文字+语音回复
- ✅ **FAQ自动回复** - Excel格式FAQ管理
- ✅ **Web配置界面** - 实时监控+在线配置
- ✅ **多平台支持** - Windows/Linux/macOS

## 🚀 部署步骤

### 1. 复制部署包
将 `wchat_deploy_xxxxxxxx.zip` 复制到目标电脑

### 2. 解压文件
解压到任意目录，例如：
```
C:\wchat\          (Windows)
/home/<USER>/wchat/  (Linux)
~/wchat/           (macOS)
```

### 3. 安装依赖

#### Windows系统
```bash
# 双击运行
install.bat

# 或命令行运行
python install_deps.py
```

#### Linux/macOS系统
```bash
# 设置执行权限并运行
chmod +x install.sh
./install.sh

# 或直接运行
python3 install_deps.py
```

### 4. 配置API密钥
编辑 `config/config.json` 文件：
```json
{
  "ai": {
    "api_key": "您的硅基流动API密钥"
  }
}
```

**获取API密钥**：
1. 访问 https://siliconflow.cn
2. 注册账号并获取API密钥
3. 将密钥填入配置文件

### 5. 启动机器人

#### 方式1: 快速启动
```bash
python quick_start.py
```

#### 方式2: 完整启动
```bash
python start_wchat.py
```

#### 方式3: Windows批处理
双击 `快速启动.bat`

### 6. 访问Web界面
启动后访问：http://localhost:5000
- 用户名：admin
- 密码：admin123

## 📋 目录结构

```
wchat/
├── src/                    # 源代码
│   ├── ai/                # AI服务模块
│   ├── bot/               # 机器人核心
│   ├── database/          # 数据处理
│   ├── utils/             # 工具函数
│   └── web/               # Web界面
├── config/                # 配置文件
│   ├── __init__.py        # 配置类
│   └── config.json        # 主配置文件
├── data/                  # 数据文件
│   ├── faq.xlsx           # FAQ数据
│   ├── products.xlsx      # 产品数据
│   └── images/            # 产品图片
├── install.bat            # Windows安装脚本
├── install.sh             # Linux/macOS安装脚本
├── quick_start.py         # 快速启动
├── requirements.txt       # 依赖列表
└── 使用说明.md            # 详细使用说明
```

## ⚙️ 配置说明

### 基础配置
```json
{
  "wechat": {
    "auto_reply": true,           // 自动回复
    "reply_delay": 2,             // 回复延迟(秒)
    "listen_all": true,           // 监听所有消息
    "voice_to_text": true,        // 语音转文字
    "voice_reply_enabled": true   // 语音回复
  }
}
```

### AI配置
```json
{
  "ai": {
    "api_key": "your_api_key",    // 必填：API密钥
    "base_url": "https://api.siliconflow.cn/v1",
    "model": "deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
    "enabled": true,              // 启用AI
    "temperature": 0.7            // 回复随机性
  }
}
```

### Web配置
```json
{
  "web": {
    "host": "127.0.0.1",         // 监听地址
    "port": 5000,                // 端口号
    "password": "admin123"       // 登录密码
  }
}
```

## 🔧 常见问题

### Q: Python环境要求？
A: Python 3.8或更高版本

### Q: 如何获取API密钥？
A: 访问 https://siliconflow.cn 注册并获取

### Q: 微信无法连接？
A: 确保微信PC版已登录，并允许程序访问

### Q: 端口被占用？
A: 修改 config.json 中的 port 配置

### Q: 语音功能不工作？
A: 检查微信版本是否支持语音转文字API

### Q: 产品图片不显示？
A: 确保图片文件存在于 data/images/ 目录

## 🎯 使用示例

### 基本对话
```
用户: 你好
机器人: 你好！有什么可以帮你的吗？
```

### 产品查询
```
用户: 推荐一款手机
机器人: 推荐你这款产品：
       智能手机A1 + 详细信息 + 图片
```

### 语音消息
```
用户: [语音] "有什么耳机推荐"
机器人: 推荐你这款产品：
       无线耳机B2 + 详细信息 + 图片
```

## 📊 性能要求

### 最低配置
- CPU: 双核 2.0GHz
- 内存: 4GB RAM
- 存储: 1GB 可用空间
- 网络: 稳定的互联网连接

### 推荐配置
- CPU: 四核 2.5GHz
- 内存: 8GB RAM
- 存储: 2GB 可用空间
- 网络: 高速互联网连接

## 🔒 安全说明

### 配置安全
- API密钥已从部署包中清除
- 需要手动配置API密钥
- 建议修改Web界面默认密码

### 网络安全
- Web界面默认只监听本地地址
- 如需远程访问，请配置防火墙
- 建议使用HTTPS（生产环境）

## 📞 技术支持

### 日志查看
```bash
# 查看运行日志
tail -f data/logs/app.log

# Windows查看日志
type data\logs\app.log
```

### 故障排除
1. 检查Python版本和依赖
2. 验证配置文件格式
3. 确认API密钥有效性
4. 检查网络连接状态
5. 查看详细错误日志

### 性能优化
- 定期清理日志文件
- 优化产品数据库大小
- 调整回复延迟设置
- 监控内存使用情况

## 🎉 开始使用

1. ✅ 完成上述部署步骤
2. ✅ 配置API密钥
3. ✅ 启动机器人
4. ✅ 测试基本功能
5. ✅ 访问Web界面
6. ✅ 自定义FAQ和产品

**祝您使用愉快！您的智能私域自动化机器人已准备就绪！** 🚀

---

## 📋 部署检查清单

- [ ] 解压部署包到目标目录
- [ ] 运行安装脚本安装依赖
- [ ] 配置硅基流动API密钥
- [ ] 启动机器人程序
- [ ] 访问Web配置界面
- [ ] 测试基本聊天功能
- [ ] 测试产品推荐功能
- [ ] 测试语音消息功能
- [ ] 自定义FAQ和产品数据
- [ ] 设置监听模式和回复参数

**部署完成后，您将拥有一个功能完整的智能私域自动化机器人！**
