#!/usr/bin/env python3
"""
测试发送消息修复效果
"""
import sys
import time
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_safe_message_sender():
    """测试安全消息发送器"""
    print("=" * 50)
    print("测试安全消息发送器")
    print("=" * 50)
    
    try:
        from src.utils.safe_message_sender import enhanced_send_message, install_pyautogui_if_needed
        
        # 检查并安装依赖
        print("检查依赖包...")
        if not install_pyautogui_if_needed():
            print("⚠️  pyautogui安装失败，将跳过键盘模拟功能")
        
        # 连接微信
        print("连接微信...")
        import wxauto
        wx = wxauto.WeChat()
        time.sleep(3)
        
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"✅ 微信连接成功，用户: {wx.nickname}")
            
            # 测试发送消息
            test_message = "这是一条测试消息，用于验证发送功能修复效果"
            print(f"\n测试发送消息: {test_message}")
            
            # 获取当前聊天对象
            try:
                current_chat = wx.CurrentChat()
                print(f"当前聊天: {current_chat}")
                
                if current_chat:
                    # 使用增强发送方法
                    success = enhanced_send_message(wx, test_message, current_chat)
                    if success:
                        print("✅ 增强发送方法测试成功")
                        return True
                    else:
                        print("❌ 增强发送方法测试失败")
                        return False
                else:
                    print("⚠️  没有当前聊天对象，请先打开一个聊天窗口")
                    return False
                    
            except Exception as e:
                print(f"❌ 获取当前聊天失败: {e}")
                return False
        else:
            print("❌ 微信连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wechat_handler_with_fix():
    """测试修复后的微信处理器"""
    print("\n" + "=" * 50)
    print("测试修复后的微信处理器")
    print("=" * 50)
    
    try:
        from src.bot.wechat_handler import WeChatHandler
        
        print("创建微信处理器...")
        handler = WeChatHandler()
        
        print("初始化微信连接...")
        if handler.initialize_wechat():
            print("✅ 微信处理器初始化成功")
            
            # 测试手动发送消息
            test_message = "这是通过修复后的处理器发送的测试消息"
            
            # 获取当前聊天对象
            try:
                current_chat = handler.wx.CurrentChat()
                if current_chat:
                    print(f"测试发送到: {current_chat}")
                    success = handler.send_manual_message(test_message, current_chat)
                    if success:
                        print("✅ 微信处理器发送测试成功")
                        return True
                    else:
                        print("❌ 微信处理器发送测试失败")
                        return False
                else:
                    print("⚠️  没有当前聊天对象")
                    return False
                    
            except Exception as e:
                print(f"❌ 获取聊天对象失败: {e}")
                return False
        else:
            print("❌ 微信处理器初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 微信处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_original_vs_enhanced():
    """对比原始方法和增强方法"""
    print("\n" + "=" * 50)
    print("对比原始方法和增强方法")
    print("=" * 50)
    
    try:
        import wxauto
        from src.utils.safe_message_sender import enhanced_send_message
        
        wx = wxauto.WeChat()
        time.sleep(3)
        
        if not (hasattr(wx, 'nickname') and wx.nickname):
            print("❌ 微信连接失败")
            return False
        
        current_chat = wx.CurrentChat()
        if not current_chat:
            print("⚠️  没有当前聊天对象")
            return False
        
        print(f"测试对象: {current_chat}")
        
        # 测试原始方法
        print("\n1. 测试原始SendMsg方法...")
        try:
            wx.SendMsg(msg="原始方法测试消息")
            print("✅ 原始方法发送成功")
            original_success = True
        except Exception as e:
            print(f"❌ 原始方法发送失败: {e}")
            original_success = False
        
        time.sleep(2)
        
        # 测试增强方法
        print("\n2. 测试增强发送方法...")
        enhanced_success = enhanced_send_message(wx, "增强方法测试消息", current_chat)
        if enhanced_success:
            print("✅ 增强方法发送成功")
        else:
            print("❌ 增强方法发送失败")
        
        # 总结
        print("\n" + "=" * 30)
        print("测试结果总结:")
        print(f"原始方法: {'成功' if original_success else '失败'}")
        print(f"增强方法: {'成功' if enhanced_success else '失败'}")
        
        if enhanced_success:
            print("✅ 增强方法可以作为可靠的替代方案")
        
        return enhanced_success
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("发送消息修复效果测试")
    print("=" * 60)
    
    print("请确保:")
    print("1. 微信PC版已启动并登录")
    print("2. 已打开一个聊天窗口")
    print("3. 准备接收测试消息")
    print()
    
    input("准备好后按回车键开始测试...")
    
    # 测试1: 安全消息发送器
    test1_result = test_safe_message_sender()
    
    # 测试2: 修复后的微信处理器
    test2_result = test_wechat_handler_with_fix()
    
    # 测试3: 对比测试
    test3_result = test_original_vs_enhanced()
    
    # 总结
    print("\n" + "=" * 60)
    print("最终测试结果")
    print("=" * 60)
    
    print(f"安全消息发送器: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"修复后的处理器: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"对比测试: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 所有测试通过！发送消息修复成功！")
        print("\n现在可以正常使用WChat机器人了:")
        print("python run.py")
    else:
        print("\n❌ 部分测试失败，可能需要进一步调试")
        print("\n建议:")
        print("1. 检查微信窗口状态")
        print("2. 确保有活跃的聊天窗口")
        print("3. 尝试重启微信PC版")
    
    print("\n" + "=" * 60)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
