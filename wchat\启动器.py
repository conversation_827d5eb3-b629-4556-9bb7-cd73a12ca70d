#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信客服机器人启动器
跨平台的Python启动脚本
"""
import os
import sys
import subprocess
import webbrowser
from pathlib import Path


class WeChatBotLauncher:
    """微信客服机器人启动器"""
    
    def __init__(self):
        self.script_dir = Path(__file__).parent
        os.chdir(self.script_dir)
        
    def print_banner(self):
        """打印横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    微信客服机器人启动器                        ║
║                                                              ║
║  当前目录: {:<50} ║
║                                                              ║
║  [1] 安装依赖包                                              ║
║  [2] 启动Web配置界面                                         ║
║  [3] 启动机器人                                              ║
║  [4] 测试基础功能                                            ║
║  [5] 打开演示页面                                            ║
║  [6] 查看项目状态                                            ║
║  [0] 退出                                                    ║
╚══════════════════════════════════════════════════════════════╝
        """.format(str(self.script_dir)[:50])
        print(banner)
    
    def run_command(self, command, description):
        """运行命令"""
        print(f"\n{'='*50}")
        print(f"{description}")
        print(f"{'='*50}")
        print(f"当前目录: {os.getcwd()}")
        print(f"执行命令: {command}")
        print()
        
        try:
            if isinstance(command, list):
                result = subprocess.run(command, check=True, cwd=self.script_dir)
            else:
                result = subprocess.run(command, shell=True, check=True, cwd=self.script_dir)
            
            print(f"\n✅ {description}完成！")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"\n❌ {description}失败！")
            print(f"错误代码: {e.returncode}")
            return False
        except Exception as e:
            print(f"\n❌ {description}失败！")
            print(f"错误信息: {e}")
            return False
    
    def install_deps(self):
        """安装依赖包"""
        return self.run_command([sys.executable, "install_deps.py"], "安装依赖包")
    
    def start_web_config(self):
        """启动Web配置界面"""
        print(f"\n{'='*50}")
        print("启动Web配置界面")
        print(f"{'='*50}")
        print("访问地址: http://127.0.0.1:5000")
        print("默认密码: admin123")
        print("提示: 启动后会自动打开浏览器")
        print("按 Ctrl+C 可以停止服务器")
        print()
        
        try:
            subprocess.run([sys.executable, "web_config.py"], cwd=self.script_dir)
        except KeyboardInterrupt:
            print("\n\n✅ Web配置界面已停止")
        except Exception as e:
            print(f"\n❌ Web配置界面启动失败: {e}")
    
    def start_bot(self):
        """启动机器人"""
        print(f"\n{'='*50}")
        print("启动微信客服机器人")
        print(f"{'='*50}")
        print("重要提示:")
        print("1. 请确保微信PC版已登录")
        print("2. 请先在Web配置界面中设置监听列表")
        print("3. 按 Ctrl+C 可以停止机器人")
        print()
        
        try:
            subprocess.run([sys.executable, "run.py"], cwd=self.script_dir)
        except KeyboardInterrupt:
            print("\n\n✅ 机器人已停止")
        except Exception as e:
            print(f"\n❌ 机器人启动失败: {e}")
    
    def test_basic(self):
        """测试基础功能"""
        return self.run_command([sys.executable, "test_basic.py"], "运行基础功能测试")
    
    def open_demo(self):
        """打开演示页面"""
        demo_path = self.script_dir / "demo.html"
        print(f"\n{'='*50}")
        print("打开演示页面")
        print(f"{'='*50}")
        print(f"演示页面路径: {demo_path}")
        print()
        
        try:
            webbrowser.open(f"file://{demo_path.absolute()}")
            print("✅ 演示页面已在浏览器中打开")
            return True
        except Exception as e:
            print(f"❌ 无法打开演示页面: {e}")
            print(f"请手动打开: {demo_path}")
            return False
    
    def show_status(self):
        """显示项目状态"""
        print(f"\n{'='*50}")
        print("项目状态检查")
        print(f"{'='*50}")
        
        # 检查文件
        files_to_check = [
            "config/__init__.py",
            "data/faq.xlsx",
            "data/products.xlsx",
            "src/bot/reply_engine.py",
            "src/web/app.py",
            "run.py",
            "web_config.py"
        ]
        
        print("📁 文件检查:")
        for file_path in files_to_check:
            full_path = self.script_dir / file_path
            status = "✅" if full_path.exists() else "❌"
            print(f"   {status} {file_path}")
        
        # 检查Python模块
        print("\n📦 Python模块检查:")
        modules_to_check = [
            "flask",
            "pandas", 
            "openpyxl",
            "jieba",
            "fuzzywuzzy",
            "wxauto",
            "openai"
        ]
        
        for module in modules_to_check:
            try:
                __import__(module)
                print(f"   ✅ {module}")
            except ImportError:
                print(f"   ❌ {module} (未安装)")
        
        # 检查数据
        print("\n📊 数据检查:")
        try:
            import sys
            sys.path.insert(0, str(self.script_dir))
            from src.bot.reply_engine import ReplyEngine
            
            engine = ReplyEngine()
            stats = engine.get_statistics()
            
            print(f"   FAQ数量: {stats.get('faq_count', 0)}")
            print(f"   产品数量: {stats.get('product_count', 0)}")
            print(f"   AI可用: {'✅' if stats.get('ai_available') else '❌'}")
            
        except Exception as e:
            print(f"   ❌ 数据检查失败: {e}")
    
    def main_menu(self):
        """主菜单"""
        while True:
            self.print_banner()
            
            try:
                choice = input("请输入选择 (0-6): ").strip()
                
                if choice == "1":
                    self.install_deps()
                elif choice == "2":
                    self.start_web_config()
                elif choice == "3":
                    self.start_bot()
                elif choice == "4":
                    self.test_basic()
                elif choice == "5":
                    self.open_demo()
                elif choice == "6":
                    self.show_status()
                elif choice == "0":
                    print("\n感谢使用微信客服机器人！")
                    break
                else:
                    print("\n❌ 无效选择，请重新输入")
                
                input("\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n\n感谢使用微信客服机器人！")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {e}")
                input("\n按回车键继续...")


def main():
    """主函数"""
    try:
        launcher = WeChatBotLauncher()
        launcher.main_menu()
    except Exception as e:
        print(f"启动器运行失败: {e}")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
