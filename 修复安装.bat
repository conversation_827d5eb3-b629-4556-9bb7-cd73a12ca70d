@echo off
chcp 65001 >nul
echo ========================================
echo     wchat安装问题修复程序
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境！
    echo 💡 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 🔄 升级pip到最新版本...
python -m pip install --upgrade pip

echo.
echo 📦 使用备用requirements文件安装依赖包...
cd wchat

REM 尝试使用备用的纯英文requirements文件
if exist requirements_backup.txt (
    echo 📥 使用备用requirements文件...
    python -m pip install -r requirements_backup.txt --no-cache-dir
    if errorlevel 1 (
        echo 💡 尝试使用国内镜像源...
        python -m pip install -r requirements_backup.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --no-cache-dir
    )
) else (
    echo ❌ 备用requirements文件不存在！
    echo 💡 手动安装核心依赖包...
    python -m pip install wxauto Flask openpyxl pandas openai requests --no-cache-dir
)

if errorlevel 1 (
    echo ❌ 安装仍然失败！
    echo.
    echo 🔧 请尝试以下解决方案：
    echo    1. 检查网络连接
    echo    2. 使用管理员权限运行
    echo    3. 手动执行: python -m pip install wxauto Flask openpyxl pandas openai requests
    echo.
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成

echo.
echo 🎉 修复完成！
echo.
pause
