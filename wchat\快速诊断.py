#!/usr/bin/env python3
"""
WChat快速诊断工具
快速识别和解决常见问题
"""
import os
import sys
import time
import psutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("需要Python 3.8或更高版本")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")
    
    required_packages = [
        'wxauto',
        'flask',
        'pandas',
        'openpyxl',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_wechat_process():
    """检查微信进程"""
    print("\n🔍 检查微信进程...")
    
    wechat_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'status']):
        try:
            if 'wechat' in proc.info['name'].lower():
                wechat_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if wechat_processes:
        print(f"✅ 找到 {len(wechat_processes)} 个微信进程:")
        for proc in wechat_processes:
            print(f"   PID: {proc['pid']}, 状态: {proc['status']}")
        return True
    else:
        print("❌ 微信PC版未运行")
        print("请启动微信PC版并登录")
        return False

def test_wxauto_basic():
    """测试wxauto基本功能"""
    print("\n🔍 测试wxauto连接...")
    
    try:
        from wxauto import WeChat
        print("正在连接微信...")
        
        wx = WeChat()
        time.sleep(3)
        
        if hasattr(wx, 'nickname') and wx.nickname:
            print(f"✅ 微信连接成功，用户: {wx.nickname}")
            return True
        else:
            print("❌ 微信连接失败，无法获取用户信息")
            return False
            
    except Exception as e:
        print(f"❌ wxauto连接失败: {e}")
        
        # 分析错误类型
        error_str = str(e)
        if "SetWindowPos" in error_str or "无效的窗口句柄" in error_str:
            print("\n🔧 检测到窗口句柄错误，建议:")
            print("  1. 重启微信PC版")
            print("  2. 确保微信完全登录")
            print("  3. 运行: python 修复微信连接.py")
        elif "找不到" in error_str or "not found" in error_str:
            print("\n🔧 检测到微信未找到错误，建议:")
            print("  1. 确保微信PC版已启动")
            print("  2. 检查微信版本是否兼容")
        
        return False

def check_config_files():
    """检查配置文件"""
    print("\n🔍 检查配置文件...")
    
    config_file = Path("config/config.json")
    if config_file.exists():
        print("✅ 配置文件存在")
        
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查关键配置
            if 'wechat' in config:
                print("✅ 微信配置存在")
            if 'ai' in config:
                print("✅ AI配置存在")
            
            return True
        except Exception as e:
            print(f"❌ 配置文件格式错误: {e}")
            return False
    else:
        print("❌ 配置文件不存在")
        print("请运行: python web_config.py 创建配置")
        return False

def check_data_files():
    """检查数据文件"""
    print("\n🔍 检查数据文件...")
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("❌ data目录不存在")
        return False
    
    faq_file = data_dir / "faq.xlsx"
    products_file = data_dir / "products.xlsx"
    
    files_ok = True
    
    if faq_file.exists():
        print("✅ FAQ文件存在")
    else:
        print("❌ FAQ文件不存在")
        files_ok = False
    
    if products_file.exists():
        print("✅ 产品文件存在")
    else:
        print("❌ 产品文件不存在")
        files_ok = False
    
    if not files_ok:
        print("请运行: python data/create_sample_data.py")
    
    return files_ok

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 50)
    print("🔧 常见问题解决方案")
    print("=" * 50)
    
    print("\n1. 微信连接问题:")
    print("   - 运行: python 修复微信连接.py")
    print("   - 重启微信PC版")
    print("   - 确保微信完全登录")
    
    print("\n2. 依赖包问题:")
    print("   - 运行: pip install -r requirements.txt")
    print("   - 或运行: 修复安装.bat")
    
    print("\n3. 配置问题:")
    print("   - 运行: python web_config.py")
    print("   - 检查config/config.json文件")
    
    print("\n4. 数据文件问题:")
    print("   - 运行: python data/create_sample_data.py")
    
    print("\n5. 启动问题:")
    print("   - 运行: python 安全启动.py")
    print("   - 或运行: 修复并启动.bat")

def main():
    """主诊断流程"""
    print("=" * 60)
    print("          WChat快速诊断工具")
    print("=" * 60)
    
    all_ok = True
    
    # 检查各个组件
    if not check_python_version():
        all_ok = False
    
    if not check_dependencies():
        all_ok = False
    
    if not check_wechat_process():
        all_ok = False
    
    if not test_wxauto_basic():
        all_ok = False
    
    if not check_config_files():
        all_ok = False
    
    if not check_data_files():
        all_ok = False
    
    # 总结
    print("\n" + "=" * 60)
    if all_ok:
        print("🎉 所有检查通过！")
        print("可以运行: python run.py 启动机器人")
    else:
        print("❌ 发现问题，请参考解决方案")
        provide_solutions()
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
